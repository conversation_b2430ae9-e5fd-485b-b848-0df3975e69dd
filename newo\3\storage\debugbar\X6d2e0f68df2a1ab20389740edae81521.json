{"__meta": {"id": "X6d2e0f68df2a1ab20389740edae81521", "datetime": "2025-06-17 12:19:21", "utime": **********.91461, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.307655, "end": **********.914634, "duration": 0.6069788932800293, "duration_str": "607ms", "measures": [{"label": "Booting", "start": **********.307655, "relative_start": 0, "end": **********.820224, "relative_end": **********.820224, "duration": 0.5125689506530762, "duration_str": "513ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.820237, "relative_start": 0.5125818252563477, "end": **********.914637, "relative_end": 3.0994415283203125e-06, "duration": 0.09440016746520996, "duration_str": "94.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45531776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02409, "accumulated_duration_str": "24.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.863099, "duration": 0.02195, "duration_str": "21.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.117}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8989458, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.117, "width_percent": 3.985}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9044821, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 95.102, "width_percent": 4.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-904961209 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-904961209\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-684161431 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684161431\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-473193219 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-473193219\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1025907081 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162725167%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1VVDJrdGZqUWJFRkZyeVYrd242VHc9PSIsInZhbHVlIjoiaVorQVNnSDRDaFhGWUVPZkx4allZODJMRG5vOHA2Sk1vdHpHZTBMbnFyWnA0MVJBT0NmMDduSTRDQ081c0ZWd1hWZUc3aXRKV2I0aWN2MmtIRWZCN3piLzJWU1hlckxWQ0tzMmdCR1ZPMHRXL3JtMSsvSktoTWllSXhvc2ZEeFJMbHFZajB0Sk82SWpkZ1RSNHRYaGowYmdROWl6OGxFeSswZGFuM2RJSG15SXNSc3N1ZmxPQksrQjIzNmZ3UEtpbGNOeFhMVk81cWoya2hGNTJnOU0xTzNyUzV2ejJwRGxQb3NrVm5NdHRoWjV2REFxUTU5MHdZVjlYMkVvS25xSDFyM2J3QWhJTXpqNXdRL3ppazgxY3BKUzBlUFdZMlAzRStMbHROdHU2T1Fpd0JVRGJBQTJoeEUzOHBrS3E5MU5uR1duMytxd3UwalRieDRscUpaTGIyRlY2TlNUWWd1SU9DQUVFemE3ZFRSU3ljSlU2QWtoVjArcDJ4NEwxRVZHbFR4eWpiQ3czRzcwYW1lVFVwdWlCbWx2LytnZXRkVm9Xd1FFMFE1SS9HNC80ZU5mbE93YXRYajh4anpKRE9xaWxFZWw5TFQ1QzVkd3hRclByYmRCSUw5NmV6VDVnamo3T1VZbG0rVW5saGg0T1BrR25jYmwyeW5yM1BNMU15bGEiLCJtYWMiOiJjZDVkMjlkOTdjNzExYTA4MmQ1N2I5YzA4Yzc0OTgwNzM1ZjA4ZGQ0N2M4ZTE5NGY3YzJhM2YzYWVlNDkzNmU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZNRXQvTWRLY3g1UjdrazlybDJSSFE9PSIsInZhbHVlIjoiNjJQOUhFZEdTU2F4T1RwTExQRXFCelpDelJzTmZFaFRuU3NxcVRMbHA3RUpDMm1uUEpBMGpBQ3J1WGNTNzVGL1FUd0dPTlZPa3ltcytQZlFRd25ERVI4YVlPR0tOS0Y0WURVL0ZQbmMyR2JBNkg0OHZPcTZscEdtek1qdm5oRTUrakxzYnNtcEhEb2VVNEJsajh6NXJNTE92aDFLbTBpMDkwajV1bGxqUHl1WVZVeXZuTzdnb2xqN0dyNFVieHdDUzJ5YkFMa2N5VU5icGMybmkyaC9KdjdzbVZSR0wyNEMwVy91MU9hR0swVlEyVTdCTnlwbUMxU1M3V2pJaG5WWk9LVStzQndZOGZSczJVN2IyTUpad1lWR3lUZFhQUGZ3UDdrUzJoNk1yblFIWlUwWHVNa0srSjNzUWtNczRpaHFlNDZ3U1h0d3NhR1p0blBXQ1lYOUpKSkFYTDJPczFJMjVNR3N4ZWdjc2dZZjlvckd0WHpvcllkMFlDVHFQWXhrdGVWM2RHTmY0RXliblFXUXNjaGhIbzQvUVFBK1JoY0l3djNCclE3Wmo4MFhDZjJqV3dZaC9xSUtiQTVXbXp1Mmp3SWNJa2lPVmxReVhHWk03eFN5Ni9EbmVJQzFHb0RORGRmSk9yVDlsYldwMkhCVmcreWpxM0lyS2QzWERyVXciLCJtYWMiOiIzOTQ5NTY0YTkwOTZmZmU4OTAwYzVmOGY2ZDNlNTdiMmE2Y2ZmODU0MWEyNGM1NjRhMTZjMWM2ZDExMGI2YjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025907081\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-271063770 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271063770\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-782740038 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilp5eU5LUzlKdmw3eS8za1E2Q3JHcVE9PSIsInZhbHVlIjoiekxxWExmNHlCWE9TbVk3bUtYWXpoQjlIYXBCZ3A2MTl1Ykt5WHRPTG11MHN3TW5iUVlKcE15KytoWHFIRy91UmVSZnVHdHgwcXZvZWV2T1lwVWNqcGt0NU55bU95N1p0UHdPMVBpVUJqVVNHa1JSSDF6OEJXaDdOdkZ5UUNyZ1p0bUVmQjdkZjVtaUgwcEV6YnRpNnQ0STVHNTdqcmYxMFBqOXp5NWozdHZvS0xWSy9mNDNHNTY1TjBoeW8wc1V6SHEwV0hqdERHRG05VTNXKzRIQ3hJeE5YYmcralh5T213MWtLN0dmZ2RvcFlMd1FKL0Y0QUZTaWV1TzA2UTRRZGIyKy90LzF6VlFsWng3OXdWak5HdmJoV2Y1cnBpMHFBdFlDNjk0UTFhQlkwOVczVXNPR084MUo5bzBCUFFPdmRWWlg0RUVrN29HN0tKTlFSMUE4SnlNVDlMNkViN0l5Z1VRWVoxVWN0UkZPNkVtcS85alc5MSswT0FLQ2Z0ZTVtNjhUN0RHcjhFaFBLdW5hRWdzZ1AyQmlLTlpxS09BWk5oQU1acEpvRjk4Z2xMZFNETlRJbEtObFRnOWNza1RHZ1Mra29raXZCcTZybXhPcmhwVlJWV1VQSHdQZ2pQencya21mQzV1RUpkM2JvVmphTHJId2djSDBzMlRqR3Y2T0ciLCJtYWMiOiJlODk3M2QyMGVkY2NiMzNmMmYzZmYwNTliYjdhMGZhOGVlOWNjOWQ4Mzk1ZjU4YTRmZWNjZWE2ZTYyMDcyODJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZGZloxNGNFYmRiMGJ3MDJCVkxZOHc9PSIsInZhbHVlIjoiVGNvY0ZmN25Vb1JMbUJwdXI3L0FLSmMwSzhNdDlXQUtEVEx5dFFEcjVDZ2RnMmRQVVF0WVpKL2lYRkNuWVcwYzZiclVPYmVnZmRpaHRYbFByK3lKSzV6RDZrcmNhQjZnT0R2THpFTXAvMmR0OTRzR3pwQ1V1RFZYVFNDdFI4cFZJSVUxbzJQSjE5YUxRQkx0TDNoQVBYWVZwTmJRK0VKcGZwSDZtTm5PMlg1WTlaeHdvM01LQU1xcHByQ0lXOXNVTkZodWRETU1qS2lGbHhnT2kxKzVrN09sVGdKQm9kWjVmcVd5WjAwN1FRbHlYdU1KMFFaQ2U0SFJ5WUVRVjN4ZmsvU3JhbjhYcHJZeHpDLzlYSmgxN2M5djdQSFJ2K0NtMW5iYkMvQ2sxQUZ2MTNhU0Z6NlBhV1hDWVR3d3Y5K0hDaVpKVEJJVFY1SVBHbFFZZzc0QnM3bmxySDMrdEt2R3F1MXlienNqcWkxVWFYaEtWVkgwUWRiRTh2RjBuOG9KbVlwWU9wSkU4U3dLWWZPMTdvVUxHZElIeE1mMzFuZkhpN0JibkFxeVVFeFU5Q2hBc20zVjA2QUFNQ1pRUittRlVjdGlJY2lid29LUElCakZlY2dZZm5XMUlaNkdyVlRRRW1uZGpERFI1Vk12M2xlQlJVbUpmRGpwdTF2aGFJNEQiLCJtYWMiOiIyYmM1ZTNjNjkwNTM2NzI2YTEyYzU1ZDkzZGU0MzM1OTAwYTc4NThlZmY0ZTliYWM2NTRiMjJlM2ZlYmNmYjIxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilp5eU5LUzlKdmw3eS8za1E2Q3JHcVE9PSIsInZhbHVlIjoiekxxWExmNHlCWE9TbVk3bUtYWXpoQjlIYXBCZ3A2MTl1Ykt5WHRPTG11MHN3TW5iUVlKcE15KytoWHFIRy91UmVSZnVHdHgwcXZvZWV2T1lwVWNqcGt0NU55bU95N1p0UHdPMVBpVUJqVVNHa1JSSDF6OEJXaDdOdkZ5UUNyZ1p0bUVmQjdkZjVtaUgwcEV6YnRpNnQ0STVHNTdqcmYxMFBqOXp5NWozdHZvS0xWSy9mNDNHNTY1TjBoeW8wc1V6SHEwV0hqdERHRG05VTNXKzRIQ3hJeE5YYmcralh5T213MWtLN0dmZ2RvcFlMd1FKL0Y0QUZTaWV1TzA2UTRRZGIyKy90LzF6VlFsWng3OXdWak5HdmJoV2Y1cnBpMHFBdFlDNjk0UTFhQlkwOVczVXNPR084MUo5bzBCUFFPdmRWWlg0RUVrN29HN0tKTlFSMUE4SnlNVDlMNkViN0l5Z1VRWVoxVWN0UkZPNkVtcS85alc5MSswT0FLQ2Z0ZTVtNjhUN0RHcjhFaFBLdW5hRWdzZ1AyQmlLTlpxS09BWk5oQU1acEpvRjk4Z2xMZFNETlRJbEtObFRnOWNza1RHZ1Mra29raXZCcTZybXhPcmhwVlJWV1VQSHdQZ2pQencya21mQzV1RUpkM2JvVmphTHJId2djSDBzMlRqR3Y2T0ciLCJtYWMiOiJlODk3M2QyMGVkY2NiMzNmMmYzZmYwNTliYjdhMGZhOGVlOWNjOWQ4Mzk1ZjU4YTRmZWNjZWE2ZTYyMDcyODJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZGZloxNGNFYmRiMGJ3MDJCVkxZOHc9PSIsInZhbHVlIjoiVGNvY0ZmN25Vb1JMbUJwdXI3L0FLSmMwSzhNdDlXQUtEVEx5dFFEcjVDZ2RnMmRQVVF0WVpKL2lYRkNuWVcwYzZiclVPYmVnZmRpaHRYbFByK3lKSzV6RDZrcmNhQjZnT0R2THpFTXAvMmR0OTRzR3pwQ1V1RFZYVFNDdFI4cFZJSVUxbzJQSjE5YUxRQkx0TDNoQVBYWVZwTmJRK0VKcGZwSDZtTm5PMlg1WTlaeHdvM01LQU1xcHByQ0lXOXNVTkZodWRETU1qS2lGbHhnT2kxKzVrN09sVGdKQm9kWjVmcVd5WjAwN1FRbHlYdU1KMFFaQ2U0SFJ5WUVRVjN4ZmsvU3JhbjhYcHJZeHpDLzlYSmgxN2M5djdQSFJ2K0NtMW5iYkMvQ2sxQUZ2MTNhU0Z6NlBhV1hDWVR3d3Y5K0hDaVpKVEJJVFY1SVBHbFFZZzc0QnM3bmxySDMrdEt2R3F1MXlienNqcWkxVWFYaEtWVkgwUWRiRTh2RjBuOG9KbVlwWU9wSkU4U3dLWWZPMTdvVUxHZElIeE1mMzFuZkhpN0JibkFxeVVFeFU5Q2hBc20zVjA2QUFNQ1pRUittRlVjdGlJY2lid29LUElCakZlY2dZZm5XMUlaNkdyVlRRRW1uZGpERFI1Vk12M2xlQlJVbUpmRGpwdTF2aGFJNEQiLCJtYWMiOiIyYmM1ZTNjNjkwNTM2NzI2YTEyYzU1ZDkzZGU0MzM1OTAwYTc4NThlZmY0ZTliYWM2NTRiMjJlM2ZlYmNmYjIxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782740038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-595691214 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595691214\", {\"maxDepth\":0})</script>\n"}}