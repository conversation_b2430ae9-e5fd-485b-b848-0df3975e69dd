{"__meta": {"id": "X6d50d5115b1b3a1500c79d4ecd7dc550", "datetime": "2025-06-17 12:19:44", "utime": **********.356456, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162783.764197, "end": **********.356477, "duration": 0.5922799110412598, "duration_str": "592ms", "measures": [{"label": "Booting", "start": 1750162783.764197, "relative_start": 0, "end": **********.265719, "relative_end": **********.265719, "duration": 0.5015218257904053, "duration_str": "502ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.265732, "relative_start": 0.5015349388122559, "end": **********.356479, "relative_end": 1.9073486328125e-06, "duration": 0.09074687957763672, "duration_str": "90.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45516912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02013, "accumulated_duration_str": "20.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.308516, "duration": 0.01777, "duration_str": "17.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.276}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.340423, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.276, "width_percent": 5.713}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.345081, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 93.989, "width_percent": 6.011}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1788304780 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1788304780\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-920509776 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-920509776\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1779092409 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779092409\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2009178888 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162773418%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9XaU9Ua0lWRXFmZUJOVkRsZlErS0E9PSIsInZhbHVlIjoiWXY0aDJxZFJjZnZMUUtCK2R1LzlsS2pDS1RNZGNLcURJOHlrQzBCR0U2WmpNYzZkN1A2M1YzSTRiVDZhVzRDNGdGemtuTGtsTkJVRXlsdEJJOWw1aG00NGxuQkUyQWQxQWp4QVpBVmlkRG1pakFTNWY1R3NtcWpBZk9HSEFqNHZ0akorRDhRc1lORERFSkVLNWxTc1BQWEZWS0RqZ2dha1dqZ0JrN2MyT2dWSGNMV1hPZ01CYmJWazVvcWFCTEpZdXBsdkpWejhaUXFqQjdGZHlQdVM0OGtaRGVLRXh5eERsZWMwZ2tQWVoxMGgrTHlCbjFQcEZQWHppbXhBNWZQZ0h5anhpWkp3aG93QTNSdnB0YXcva0ExUXRDNTJTbTQxdEowM0MrcVpPNXJiMWlGclZLTnhDR2ErTXVFR1kzeS9oaW4wL1ZCQnpqRk5DRUNLUGNaWjlIQmRid1pCT0d0c3JzK2FNbXBQZTdYdFhxYTRvMnppN3JKbStlUHJjWE1ETnVuNHlubk1TYkdDRzJWUE9wZGVBZUxNZDZFbCtFWEVKRlB3SEZJM1cyWjVIYUN3VFBOV0RWeU1QS1FqR0tpNDIyeWZxcGdRbmtJTTdmbUZMbzhmNlp3QkMxL2xqRVpzMVFpZUk2NVVpSjVFTUI2ZG5KTUlVQjRQNkZ1WW9pVWEiLCJtYWMiOiI2N2Y1YTRlYTk3ZjkzNDlhNTEwZmEzYzRjZTVlNTAxZTMwNmE3NjcxNTc1MWJlNGRkMmMyODA4ZmEwYjA4ODYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFwYVJoMnNwL2RRSE5uRUhpSHNabFE9PSIsInZhbHVlIjoiK0ZWVmJuMzduaTQ1SDE2K0xoblo0dXJYWDJQRHpIMm1KTHlYRlRMOU1vcFRRRTFsUXpibEd1Zi9HYVNGMk5wS1R0bU4rMGNxZmlOTGIzTktNd0NDOXdwRytjVDhMNlhvZExwQ1ZVaFR0T2NydmZYc2xpSzRXUGFBcnIwdEh2VFkwNktXeUFCdjlrS3VJclloQk5rMGpsL1hZQW5rSUM0dUg4dzVDQ2JyY0pBNjhIeFVBUDgxdEc5T0Nyb3gyMTFxdVBGRmh3R2Nab3BodDlKdXpaL2lvdWRxOThvU3IvaW5QUE5QczY5cEJDNzBTSGJrWFh0R1ZNb0VRN0d2Q1IyRGo4end3dFBtaHZvaVpOazgzem5KSVY2TDN1c2VUSmd5cWd1cGt0Y0wwczNvK0R1eHUzaTkrVmlKdXBhc0VPRXhTWk0vcmtXRkxKZ0JNcjBHdkd2YnZoRnFFQ2NCdGZDRE1uMHN3RFZoRzVtYWt5TTVVbGN1Smxqc0xhRFp2YUhDN1hmK0tSdWpKb29RRnB0K1VyUi9GZmc3clhQby9yVlQ4L2pEWTJoRThOUWVYTUsxRXBMRDdsQXRCMEEzWlhTcWo5Uk14T1FSd1hRdE90U1NVSlJROC9vcU9uY1orMjVaNUlURTRBLyt6YytjZTFKZjlxOEVPc3V5aXV2WTU2blAiLCJtYWMiOiIyMjE5MGQwMTUwZTViY2QyMTBjODdiMjhjZjI4ZWRlMTJiMDM1MjUzOTZjMmIxODQ0ZDI5Yzg2MzY1MzQ4MTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009178888\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1047137093 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047137093\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-198747284 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZVcVRvcmVVMWxEOG93cXpIdkZBK3c9PSIsInZhbHVlIjoiQS8wdENhRUlPZzlodlV6TTNab3FqaUxoZmRVeWFVODlJY25BTmFTQ2lZU213b3RrSVBpeDE5VGFVOEpFOXMveWJWbVRiZFpEay8wY2NhZU9JbDJGRnBZNHpoVU9ZZ3l5QWNybmZqd1dkTVFJTzdhMUk0QVFGalJRZVJwdHBUWU5vNDNXdDVWNUdJWUlzekZpSDluOXVzdElCQUpqb01jZHBCMldQUlNhalg1YjdDQjBIMmgrb2xCSXh3RVVQekFzcmNjVzFISVBNKzlTVWtVQjlHQkkzV244SkEzemp6NlVUYmdsVFFWRUc5QzA0eHh1MU9ILytJSldjTFlqc0hhSVZTKzFaZnN0YklseEJSR1lGcUE2VU05UDVxV3hXKzNSZGtKSnZFdndLWVZ3OC8wU3FxYUV2M3dQQ1RBbmtoVVhZR2ZaU2pjbzNZUk5RRklEeHlxUy9xK2dMRzhLTVNLVk1BcElUN3JzeTB6Tm4wemRLOG9RbkF4a1Y0Q2orVXFhNGVoeEViSjFhQjdNaXFjemVtdi9uNm8xM3JFR2dmWC80YXIwZlozb3lxcEl1ejBIcm9nbklBRjR4aFdGVnFkWXlnYTBrc2M5WXd4WU9RTlY5ODYrM09vMUtRWEJGT05IaTFWQlFjMmp6SmdHZnhGNXBNZ25vQk9NMVdHVTJBVTIiLCJtYWMiOiI5MDdjZTA5OGE3ODZjMDY1NTUyMDdmOTYwMjQxYmIzODZhYTMzYTJkMWM4NWYyNTI1Mjk4NWIyY2UxNzYwMjgwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjZlOHpKci9UK2l0ajc3Y3J1Znp4RWc9PSIsInZhbHVlIjoiYlZvSWl6V1dnQnRLSDJhWkVTcDZwY3ZVVlhwTkh5TzdPekdUKzlrVnZNSG1GWnZIYnBqdTVkUXZmOXFKczBHUEFJQTg2MVM4RWJ4ZlAweCttVWV5QXdMSDY3Q1ZvaHhRMW5Qd2NCTVFCNGoyTGJ0aCt4TFZjVnNSNzl2YkVGclozUFNodnltamZGaVYzVlM5N1pyZGt6OXRQR1FvOEVqU2xIYmxrV3RLS0E1MTQyQkFEOEtiMnZGVXcvYlpoMDdoWVFXYjVoRCtnaVphTlE2aEdEcXM4S0xkcGw1TlBxVWNKTHFyay9zNEFScktOdGlXcUU5cHZVT0pIYzIxaEpobEZwZnZUSnZQK2svY0w2VXUwZWttOUJHemFJY0xOSEUwN3MrZlYwTXZ3YzFEK3B5SUZnd2tmSnVzam96eFhTNko5VFJwbSs1RW1mRFZ5UmtyeGxpUDQ4c1B2SjREQndwbys4ajJGUDVKUFg1UXNWSGV5SDJHWUh6MzE4ZHJubmVMM2xyRG1FRWsxL3pjY0Rqd0xxZzVSZ3A4RWZkS2tGTlRha09rMUExbHk3SFUvTnFNVjZTMlJEM1Yrb0NzSGs2Q0twSStkeHRKVVVFVFpreHVwT21HblVteXhCaTVITCtPd0d1cVFsZUhVUEtPODVrOTIzVjBjQkd4bTZmQnRJWkIiLCJtYWMiOiIzMzA0YzM2OTc1MGViMzNjOWFlODBhYjRkN2I3YmI2YmUzNGQ4OTE4NmVkZTcyNjY4MDRiMmI3YTY3MTQ3ZmEwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZVcVRvcmVVMWxEOG93cXpIdkZBK3c9PSIsInZhbHVlIjoiQS8wdENhRUlPZzlodlV6TTNab3FqaUxoZmRVeWFVODlJY25BTmFTQ2lZU213b3RrSVBpeDE5VGFVOEpFOXMveWJWbVRiZFpEay8wY2NhZU9JbDJGRnBZNHpoVU9ZZ3l5QWNybmZqd1dkTVFJTzdhMUk0QVFGalJRZVJwdHBUWU5vNDNXdDVWNUdJWUlzekZpSDluOXVzdElCQUpqb01jZHBCMldQUlNhalg1YjdDQjBIMmgrb2xCSXh3RVVQekFzcmNjVzFISVBNKzlTVWtVQjlHQkkzV244SkEzemp6NlVUYmdsVFFWRUc5QzA0eHh1MU9ILytJSldjTFlqc0hhSVZTKzFaZnN0YklseEJSR1lGcUE2VU05UDVxV3hXKzNSZGtKSnZFdndLWVZ3OC8wU3FxYUV2M3dQQ1RBbmtoVVhZR2ZaU2pjbzNZUk5RRklEeHlxUy9xK2dMRzhLTVNLVk1BcElUN3JzeTB6Tm4wemRLOG9RbkF4a1Y0Q2orVXFhNGVoeEViSjFhQjdNaXFjemVtdi9uNm8xM3JFR2dmWC80YXIwZlozb3lxcEl1ejBIcm9nbklBRjR4aFdGVnFkWXlnYTBrc2M5WXd4WU9RTlY5ODYrM09vMUtRWEJGT05IaTFWQlFjMmp6SmdHZnhGNXBNZ25vQk9NMVdHVTJBVTIiLCJtYWMiOiI5MDdjZTA5OGE3ODZjMDY1NTUyMDdmOTYwMjQxYmIzODZhYTMzYTJkMWM4NWYyNTI1Mjk4NWIyY2UxNzYwMjgwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjZlOHpKci9UK2l0ajc3Y3J1Znp4RWc9PSIsInZhbHVlIjoiYlZvSWl6V1dnQnRLSDJhWkVTcDZwY3ZVVlhwTkh5TzdPekdUKzlrVnZNSG1GWnZIYnBqdTVkUXZmOXFKczBHUEFJQTg2MVM4RWJ4ZlAweCttVWV5QXdMSDY3Q1ZvaHhRMW5Qd2NCTVFCNGoyTGJ0aCt4TFZjVnNSNzl2YkVGclozUFNodnltamZGaVYzVlM5N1pyZGt6OXRQR1FvOEVqU2xIYmxrV3RLS0E1MTQyQkFEOEtiMnZGVXcvYlpoMDdoWVFXYjVoRCtnaVphTlE2aEdEcXM4S0xkcGw1TlBxVWNKTHFyay9zNEFScktOdGlXcUU5cHZVT0pIYzIxaEpobEZwZnZUSnZQK2svY0w2VXUwZWttOUJHemFJY0xOSEUwN3MrZlYwTXZ3YzFEK3B5SUZnd2tmSnVzam96eFhTNko5VFJwbSs1RW1mRFZ5UmtyeGxpUDQ4c1B2SjREQndwbys4ajJGUDVKUFg1UXNWSGV5SDJHWUh6MzE4ZHJubmVMM2xyRG1FRWsxL3pjY0Rqd0xxZzVSZ3A4RWZkS2tGTlRha09rMUExbHk3SFUvTnFNVjZTMlJEM1Yrb0NzSGs2Q0twSStkeHRKVVVFVFpreHVwT21HblVteXhCaTVITCtPd0d1cVFsZUhVUEtPODVrOTIzVjBjQkd4bTZmQnRJWkIiLCJtYWMiOiIzMzA0YzM2OTc1MGViMzNjOWFlODBhYjRkN2I3YmI2YmUzNGQ4OTE4NmVkZTcyNjY4MDRiMmI3YTY3MTQ3ZmEwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198747284\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-910244014 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910244014\", {\"maxDepth\":0})</script>\n"}}