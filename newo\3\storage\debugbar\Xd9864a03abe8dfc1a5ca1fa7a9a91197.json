{"__meta": {"id": "Xd9864a03abe8dfc1a5ca1fa7a9a91197", "datetime": "2025-06-17 12:19:32", "utime": **********.098762, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162771.472185, "end": **********.098784, "duration": 0.6265990734100342, "duration_str": "627ms", "measures": [{"label": "Booting", "start": 1750162771.472185, "relative_start": 0, "end": 1750162771.989902, "relative_end": 1750162771.989902, "duration": 0.5177171230316162, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750162771.989919, "relative_start": 0.5177340507507324, "end": **********.098786, "relative_end": 2.1457672119140625e-06, "duration": 0.10886716842651367, "duration_str": "109ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.024149999999999998, "accumulated_duration_str": "24.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.037251, "duration": 0.02209, "duration_str": "22.09ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.47}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.075744, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.47, "width_percent": 3.975}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0870888, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.445, "width_percent": 4.555}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-979599889 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-979599889\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-590680371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-590680371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-194257182 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194257182\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1385189183 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162761627%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR1THZNL2hrdUpMSWpJRHovMnBqdVE9PSIsInZhbHVlIjoibXRMNC9WaFUxWUhGc1BxNEdocGZObmFRanhMUm9XU1RmQWhFMzNBYjBxZENhdkJ1WVE0dllhMExsUlRFdlpIR2RGOXpJR01CRkdEcDExaUNQaUNRYzAzOXhzOUN0cE0xYUZOa0xwb2FRenFwRVJ4MkZrcU1yWkRhbHByUkZ6SERFc3RmY3ZocjBWQVk5Rld3OEFTWTFhS2NNK2NPVHloZ0V4cHFKazZNYjN0US9TSmRDS1ZiWnFuOXp2ZnFBOUlsVmtnQ2ttQ0ZvRWpLc2pQaXFEQjgvSmMxRms0VFg4L3o4K1R6T09tZmw1SHhMV2JVSmhtMWdxSGhJU0NTQkVrTnJUc3dpZFlzRThJUWZTWE5rS3h4emJwN1YzUGtGYUJlM2d4SHgwMXVqa1MvMXlYcFZjSWx3TzJjOTU0ZTZwblFLekM2KzgxcnhDWXovaXZSRHl2VGNkelNzdE1HSElWaVMrNFdXZStjY2x5aUlQdkc5UEQzUjNUUSszRkQzM1ZpQ1ZrL3NOUWVHTWxQekEvOHZqbm5lV0xVWTJ1SWRQcGZhOHlJeW1Galc3SmtsQzJ3Y0RPQnRJOEtDNWtaajlZR1FXSVhsMUVBajNtSWQ4ME05MDFsdEpucmxNWHFFc3Y4elAxZ2dtUmR6TnloNjFYaEhOazNpUHNBT25PVTYybUgiLCJtYWMiOiIxN2ZkMjU4OTJlOGI2NDVmNDkzZjA0ZGMyOWJiYmU1NzI1ZTUwY2IzZWZhYjQyMTMwNmI1YzVjOWY1YTNlZjE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ2WEUwTVBQZVM1TEZoVEphRmtJMGc9PSIsInZhbHVlIjoiMVFoS2QzMHQwalEzLzJhMStjZVJpWUlaZVdrZEZBb0VHTkZsMHdxMWRCeTh2UitsTVR6NjVoRWFZbkxhaUdpRWtWaEk5UCtheEl3MU45RTFQK3RvQSs5RVhXdjhKQmxsWEFZOFhqWTRkYXNtKzlDaVVuTEtJbVRIY0V5dVBBQ3FGWWd0dnpGek5nSlhKNFRVUzhSeFVCTlNjSGtqekFrZ2tzQ0JRSzJlM2lFemFlQnR1UjU3Ymc0akFGTWl4dFB6QlpIeWNhaktlcTdKMVhqUTN4RmEwV3puRXJoRDJBRnR6bkpLeWdWZ2c0ZlpoYkVCSkt5N0FPVitSK2wxWi9NOXVpVnhkeXNBQkNFd2NlbG01WFVROUROSk9zSkVtS1VPYy96VmNEamN3TXlMcDJTU2swUjRaQ3dsUGdnbHF4V1lMQjhiN01pcndLVVBhL3N3VGU0RnAzRzNrckk5elJUc1pVWnQ2ODhXY1VSbGh1VURsbkdtSkZzSW9qZnNVRm0wbHd5bnZzZmdsSzMyM3RNUktuaXJqUWJQZTY2K1A5S044a3lWSWVEL04yK3lBcHpjRENteDdJdS8vek5aVDV2NWYvR3NpMll0alRnbUlLM2NyNlRXaDdzY2d3Z1hCWUVGWnY2a0FPRmFIZDNrVHUxSUY0WTYveE9oWlg5eHliaWMiLCJtYWMiOiJhMmY0MDUzZTY0Y2E4NjFmMDdlMmU5M2NlYmQ1N2JkNzE1NTg0YWJhNDQwZTUyMjhlNDE2ODI1YjM0OWY3YmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385189183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2132745906 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132745906\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1923467128 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJKN1g5ZUNITnptZVhHV3ZDdFB1aEE9PSIsInZhbHVlIjoiTzJkNGFsUm1zWk45cytXNEZHT1UrSXVsenAwdXBGbXhsL3I4ZktudENmYTJrcTZkcjZKOFF4ci9TYUl2NjVmYTdHVDlNQ0ZlVUtTVll1bTROZUVFQzVUZjA2eTkyeHlSL3ZrSHF1dURLVERBT2RROW5SUnhZL1dkUjdhMXQxcEU4emlVQ05XTVFaaGlsQXE2emFPcVhvQ1RVUDRlT0dCWkJRVzVsVkpGZGRadndLUEd2dEs1anBtdGVQUzE5OHVzRVVkQ1FtN1QrdFQ2VGlNNnFMdUI3VXJCMm9GZEFXcS9YLzM0c0o2UWpsYm9tbmRJQUFNLzNNM1NaMjBKcjBudjRLbVZXeU9hV251Ulc0QnlaaDF0czJkUUZwSm5OQ3BzT3owd0J2R3lZTjFPenhEUnJEcklTTXd1WmZrc2J5THFINEJFN1JvNU1PclpPQkwweld4ZnNIdzZTUEwvalIvdnhjR3BHbHZaTnBuS2pTbXptTER3UEh0WWE3SkQvNDFFcUhQeURXN1JLcW1mTDI5UGlHSlM0OVRyaTRjcGMrMXVFUHhLZ1A5UXh0RzJKTW40UHMyZlM0WUZpZmdCcWc0SmJhaEZSWWZDb3hnd3JtOThZODZYMTlHdHBzSGt0Zko0bFg0ZEVPMmI1R08zM3duMDBYSHZNNkJBVGlZaTJSdkoiLCJtYWMiOiJkOWVhMmJmNTJhNmU3ZTczZWFkNDVmM2IzN2RiOGM2NTJjNDJkNjA3YjA1ZWI5ZTdhZDhhM2FkZjZiNTU5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlE5a2Z6U2l4eExZZGRCTlFoU3dpRFE9PSIsInZhbHVlIjoiSGZmYnl6UXozMzQzakZ0TkxTengzNTFpRnV0eEJjRTA1TnVnYzJxMlMveHI0eW9ocEdzbm5QcTJxOEJ4SmREaVdRY0tOc1lPNG9tZGhzVVlGVnNGTW1uMW9MNUZFTlJkd2haUTBjWmRJTVpoeUNwMkg3VVRyNFJsOWZqeUxSNlFxaVNCMHRpYkdYZmNqVGhWVFNxcTQyY0prUzRxTTVwd29TeTg4blBsK2FzbnkvempIU2J2TW4xcUpVcnlxYzNYdDlPRjJrOHFDV1Q5dGxsZmFxSkhmSWkvbjdFbHNuU2hOckZwTDZEVVNDUTNMK0c3Z0IrcVZvMHBqS1picWtHS3JOaVhFbXRWNU5sdjRPdUVsb1FxSDgzdGhUbks4WXdrdDk3WGZMSE1DNmVPQzdpL1BiYks4MHVHUmRMMzl5S3YyeHRTc2xpcDVsazRBNXhPcThlc3dwT3ptc1hyeHBxdEd6bDJMV2UwVDB2ek5yMmpiQUdPUEluTWhtYW9OZ2FibnExSkhXSzlXWHJBL3I2VklMU3d6S0VpZXhjN01LUVlYTkt4TThZeFJPVWtQMW5ONnM3SUpKTnVlbHNuditCSmExVEhsN0FQK1F4UDk3ZWVaaUxMcUVscVlBZHExS1BSMlc1SFlaMVUrTFJtam1BRTlRVVk5Z3c0c2M3WHpUNkIiLCJtYWMiOiI0YjAxZGQxYzEyODYxMmMzYjk2Y2EyYjVhNjNhYWY3YWY0ZDc5MDczNmM3NjMxNTAwY2M3YWZlOTk0ZDNiZjFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJKN1g5ZUNITnptZVhHV3ZDdFB1aEE9PSIsInZhbHVlIjoiTzJkNGFsUm1zWk45cytXNEZHT1UrSXVsenAwdXBGbXhsL3I4ZktudENmYTJrcTZkcjZKOFF4ci9TYUl2NjVmYTdHVDlNQ0ZlVUtTVll1bTROZUVFQzVUZjA2eTkyeHlSL3ZrSHF1dURLVERBT2RROW5SUnhZL1dkUjdhMXQxcEU4emlVQ05XTVFaaGlsQXE2emFPcVhvQ1RVUDRlT0dCWkJRVzVsVkpGZGRadndLUEd2dEs1anBtdGVQUzE5OHVzRVVkQ1FtN1QrdFQ2VGlNNnFMdUI3VXJCMm9GZEFXcS9YLzM0c0o2UWpsYm9tbmRJQUFNLzNNM1NaMjBKcjBudjRLbVZXeU9hV251Ulc0QnlaaDF0czJkUUZwSm5OQ3BzT3owd0J2R3lZTjFPenhEUnJEcklTTXd1WmZrc2J5THFINEJFN1JvNU1PclpPQkwweld4ZnNIdzZTUEwvalIvdnhjR3BHbHZaTnBuS2pTbXptTER3UEh0WWE3SkQvNDFFcUhQeURXN1JLcW1mTDI5UGlHSlM0OVRyaTRjcGMrMXVFUHhLZ1A5UXh0RzJKTW40UHMyZlM0WUZpZmdCcWc0SmJhaEZSWWZDb3hnd3JtOThZODZYMTlHdHBzSGt0Zko0bFg0ZEVPMmI1R08zM3duMDBYSHZNNkJBVGlZaTJSdkoiLCJtYWMiOiJkOWVhMmJmNTJhNmU3ZTczZWFkNDVmM2IzN2RiOGM2NTJjNDJkNjA3YjA1ZWI5ZTdhZDhhM2FkZjZiNTU5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlE5a2Z6U2l4eExZZGRCTlFoU3dpRFE9PSIsInZhbHVlIjoiSGZmYnl6UXozMzQzakZ0TkxTengzNTFpRnV0eEJjRTA1TnVnYzJxMlMveHI0eW9ocEdzbm5QcTJxOEJ4SmREaVdRY0tOc1lPNG9tZGhzVVlGVnNGTW1uMW9MNUZFTlJkd2haUTBjWmRJTVpoeUNwMkg3VVRyNFJsOWZqeUxSNlFxaVNCMHRpYkdYZmNqVGhWVFNxcTQyY0prUzRxTTVwd29TeTg4blBsK2FzbnkvempIU2J2TW4xcUpVcnlxYzNYdDlPRjJrOHFDV1Q5dGxsZmFxSkhmSWkvbjdFbHNuU2hOckZwTDZEVVNDUTNMK0c3Z0IrcVZvMHBqS1picWtHS3JOaVhFbXRWNU5sdjRPdUVsb1FxSDgzdGhUbks4WXdrdDk3WGZMSE1DNmVPQzdpL1BiYks4MHVHUmRMMzl5S3YyeHRTc2xpcDVsazRBNXhPcThlc3dwT3ptc1hyeHBxdEd6bDJMV2UwVDB2ek5yMmpiQUdPUEluTWhtYW9OZ2FibnExSkhXSzlXWHJBL3I2VklMU3d6S0VpZXhjN01LUVlYTkt4TThZeFJPVWtQMW5ONnM3SUpKTnVlbHNuditCSmExVEhsN0FQK1F4UDk3ZWVaaUxMcUVscVlBZHExS1BSMlc1SFlaMVUrTFJtam1BRTlRVVk5Z3c0c2M3WHpUNkIiLCJtYWMiOiI0YjAxZGQxYzEyODYxMmMzYjk2Y2EyYjVhNjNhYWY3YWY0ZDc5MDczNmM3NjMxNTAwY2M3YWZlOTk0ZDNiZjFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923467128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1322393540 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1322393540\", {\"maxDepth\":0})</script>\n"}}