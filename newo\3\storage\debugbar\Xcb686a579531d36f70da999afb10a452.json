{"__meta": {"id": "Xcb686a579531d36f70da999afb10a452", "datetime": "2025-06-17 12:13:18", "utime": **********.943368, "method": "GET", "uri": "/product-categories?_=**********143", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.30973, "end": **********.943389, "duration": 0.6336588859558105, "duration_str": "634ms", "measures": [{"label": "Booting", "start": **********.30973, "relative_start": 0, "end": **********.813023, "relative_end": **********.813023, "duration": 0.5032930374145508, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.813039, "relative_start": 0.5033090114593506, "end": **********.943392, "relative_end": 3.0994415283203125e-06, "duration": 0.13035297393798828, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48278000, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.020040000000000002, "accumulated_duration_str": "20.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.858045, "duration": 0.00594, "duration_str": "5.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 29.641}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8767889, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 29.641, "width_percent": 5.639}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 16 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["16", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.903398, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "ty", "start_percent": 35.279, "width_percent": 8.433}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.908047, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 43.713, "width_percent": 6.487}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.918263, "duration": 0.00873, "duration_str": "8.73ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "ty", "start_percent": 50.2, "width_percent": 43.563}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.931841, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "ty", "start_percent": 93.762, "width_percent": 6.238}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductServiceCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 16, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-287917260 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-287917260\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.916059, "xdebug_link": null}]}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2016516284 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2016516284\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1043102381 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_</span>\" => \"<span class=sf-dump-str title=\"13 characters\">**********143</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043102381\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1089416551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1089416551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRJVHRiTzhkeUk4emJYWDgzNG5JZGc9PSIsInZhbHVlIjoidktVODRFcmRkcDdwQ05uSnFqcE1mbEx6YzE4Uk00dXpLaWhDdFEweU1SZURFNnY2Zi9FSFRkTmdMYmUxcFUxd2R2SVp4MkdSWTZoOXZ0YkJwaHB5WU9RcGpIYVJxUTY3VHlUYXJzc0t6b3lMMll5MjFOcmtMeE9CVzhrUHVnaUVsbFlYQzNyRGE5RFVzQVltSkVXeFR3NUVzVXVrSUVVWk9razN4YmY1VHRxUklEV2tBaUllRC96SFRqZ0d0WG5OUG93aTJ6MGh0RTdIeit6Z0xUWm1HanlQUGYxWUxrbll2azhTWjg0MklseGlURTBJUmllczU4VHNmeHpDT0cxSnFPbjZsRXhJNm9nYkFpZlYxK1BCWGFTWnZYQVJrdWUzd0V0TVRpOGxxN0hRYTBUNDl6R2RyQmVBcTZlVEtUZHJlV1FrV0t5T0g3N2lPbnJmODBKeEJEOUFGQVJsWUdLU0phSkxvNVhBODJVdTZkRE04Sll5RlpZL0dDUklrQ2NoQ0d3TWlibTlvRm1lTnNFZ1FSZDl2NDg1VEF4MzFOZ09CMmFFam9rcllYRmNvQTlvRC9aRnAwYzRQVTNOSVB2TXBKVGthVjFMdkhqWTZIakUrckJhSmh1T1R3OUgwUTdlVmxwaW5TTHJJQ2h4cE5rUWpGYjVFeGhPblB5cG5JMnoiLCJtYWMiOiI5NTAzM2VlY2E2YmJlNmEwYmFmOTc2MTE1NDliN2EzZDRiODRkYTM4YzI3MGU0MGJjNmVlMGY1NTFlYTdiODNlIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6ImdDVEdVLytkRzdDTGNTY1BKMkVjWFE9PSIsInZhbHVlIjoiMDFjZStSUGtyWEMrQVh6VGJTYTNjazJiL2NnOHBTajdydVFPdUlnYStKTlJtQUVjRU9pWXdSSE4rZXczbDk5dHo4Y3ZCN0dtM25wR1hjTzZ6R21neTZVWmh5UHN6RXNlRExpRllTekFGM3VmQVhxbHVqS3RQUXUwdVpVZTlTc200NVFBdUk3R29QQTNkLy9iQW4vTitBWWdOMmdMTnByQm9GVDlqb1ByYXU2anVYV3NIQjEwT1FGVXJKVmh6MEFUM2dCUUtLaDhXd3VMeldlYVN4Q0RhL1h6ckloekFtOUEwdlBRU3hvbGtucjUxWmIzNWRBY0Vzd2NKNUZ0aEY4MlZOVG9vbG9mbGZ0bndvWG9IaXN2ckw5QnpIU2ttL1loeCtETUtTQXkrM0JwdHg4UFREVy9xZDYyR0oycU9nUElsWlZDYWs4ekhIZ1RNOWYvVUlub2hKNWo2bk0wTkppekxjNVg5dmYvS0d3bzdNZjkraHNySGhqSWZYY0RnRUZOcjd1NUhiYlVOZ3JFcFlSR1c3dEc3K2ptTTg1K05yMUxZbVhRcTJ1RWZBdmVXYitxZjBSVUVEeUFLWUZrd3p4WWNqa2FtSStuZ1dqc1pLVFJRMlZZcyt6cEkrTStWM1R0RlBpaWRpT0RGbnQ0THRuT1dyeGxRd1hJalFTcDhjZVMiLCJtYWMiOiI4NDdhNzc3MGFjNTBmMWNiMzFkZjdlMjJjOThlMDYwMmFhMjNmOWQxNTM1MWE4YWVjZmM5Mjg1YWYyNjc4NWM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-431658676 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431658676\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2031576245 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:13:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZ6REpBdVJPazY4Y1FFcCsvampsRkE9PSIsInZhbHVlIjoiSWJNSHhQbVAwTExZT0txWW1iK1JGY2tIckpPME50eURvKzluR2hHeW5nOW1TU3J6R05HTVdDdEMxNmRhYnMxeHR2RVZzRlRWQnlkSDRRcEhVR3hDUHF1aWhveGU5bTUweERDRVEvVHA1cjBHU292TUdmbitjMmcrczNPNWVHVUFmTno0OVF6bW4xb2hOQllwSmlpSitFTmdkTXlkY1pXS0NUOHBQOE1sUlpGcmJLS05ENVZYNW9yUWxaZWRUUGlTZ3JQb09iZjU0ck1JNTFmb2IzTjE5RFFoY0tnSG1Gb2xQclhVOW1xUkJ6MlQzSy9icWpCVGY1bjlWMFJaYWx2QmM1NHltUkwwRFZCVEJhVW01ejlCM1hWNm9aZXJOTDRVVDc4cEpSajFxR2xiNDVPUUVYenZFWDBXYTczNmZkbGJRRHdOcFE1ZnNSMFp2eGhIcVdGNHVGMmZOZkYzYWZUSG9MdDZoRUQxQmgyY25rVUFOTUFnQkZmV0NCN0pNYjVHb1VNbHJMdEdkc0tYWVdUYVRwM2g2Z0ZJVGVSdjlUdTNQQVFUWGViWkRzb1JnN0x5R1BwenBYczZheXd6NGVqZFVPRVZHVnl6NkdDM3BEZ24yQS9sZUZiZXd0dEVMdVgrN3NQZDBLdHJKeldFeDN3a2VrOFE2U2lJZXFER1BhMTgiLCJtYWMiOiJkZjg2ODVmZTQyMjUzMGRjYWUyY2E4Mzc3MTE0NmJlODdjYmZiY2YxNDU3NWFlODNjOGI0NjNkZjJlNjNkZDVjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:13:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IjBNTVE4TnVnOENuZmdnNWJscWliS3c9PSIsInZhbHVlIjoiVkU3MkdOejc1OTlOMHRjT0RET1h2YzdSTTRlbkF6bVZPSEtneXJCdDdFcDRhbVR1UjFFc3ZzZHFKWXdjUWgyWWh1d2VUN1Z1Y2g3UUFLMzEzamo2TG9MK3I4SXlLMUQyVXR6MklTdEZSaW8zVWcvRjVhT1FPNjhUQ05VaUZiR3Y3YnlXb1duQ2ExZUpnOFBqUlhLeWg5WTlCVXlBSnhoNTF4RjJwWFFLc3NXcG5qcEhYVHd6UGJDdyszQXU5ajljNi9EZXdzaWZWdEFWMGN6OWQ2MFFLSnVEaUVLTTZPU2JRQmVFTm9VWnV4Vy8xeDZuQkFleHI0UmlRWHhPMCsrQ25oSzlkazNoTGpFeTY4VXJEOGVYa3B4Qi85M2JORktPWmtMQ0t3RFBzbC8zYXRzQmpLaU5lYWxKT3BvZTJwSVdoeHhNNVk5WHNraHRPUXNJSXVmS28weFNpMWsxdlAvVE9TNkpYVFRRZTl4aGk4OXR0YkZmbnVORlh2cG91ZzdkOUdPeW9YWGUycXBUY2JnZHQxbysweU9BbTJWTGs3dythTGoxWmhYR0IzckF6clhBQ0ZVRytCbHNhdWNCQis3S1kyeHpmcCtjSHlZQW1YVlBMTUxHWmlFK2N4bi8xZlczZE9hWDBFWEk2clNVQ29pUEZHR2lmRlBtWXRJNm12ZFUiLCJtYWMiOiIxNjEwOTdmNzM4N2Y1OGNiYmQ1M2U4Mzc3OWMyNDRkMzVlZDllYTRhYjk0NmFlM2RhMTc2NjhmMTRiOTMxMTZkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:13:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZ6REpBdVJPazY4Y1FFcCsvampsRkE9PSIsInZhbHVlIjoiSWJNSHhQbVAwTExZT0txWW1iK1JGY2tIckpPME50eURvKzluR2hHeW5nOW1TU3J6R05HTVdDdEMxNmRhYnMxeHR2RVZzRlRWQnlkSDRRcEhVR3hDUHF1aWhveGU5bTUweERDRVEvVHA1cjBHU292TUdmbitjMmcrczNPNWVHVUFmTno0OVF6bW4xb2hOQllwSmlpSitFTmdkTXlkY1pXS0NUOHBQOE1sUlpGcmJLS05ENVZYNW9yUWxaZWRUUGlTZ3JQb09iZjU0ck1JNTFmb2IzTjE5RFFoY0tnSG1Gb2xQclhVOW1xUkJ6MlQzSy9icWpCVGY1bjlWMFJaYWx2QmM1NHltUkwwRFZCVEJhVW01ejlCM1hWNm9aZXJOTDRVVDc4cEpSajFxR2xiNDVPUUVYenZFWDBXYTczNmZkbGJRRHdOcFE1ZnNSMFp2eGhIcVdGNHVGMmZOZkYzYWZUSG9MdDZoRUQxQmgyY25rVUFOTUFnQkZmV0NCN0pNYjVHb1VNbHJMdEdkc0tYWVdUYVRwM2g2Z0ZJVGVSdjlUdTNQQVFUWGViWkRzb1JnN0x5R1BwenBYczZheXd6NGVqZFVPRVZHVnl6NkdDM3BEZ24yQS9sZUZiZXd0dEVMdVgrN3NQZDBLdHJKeldFeDN3a2VrOFE2U2lJZXFER1BhMTgiLCJtYWMiOiJkZjg2ODVmZTQyMjUzMGRjYWUyY2E4Mzc3MTE0NmJlODdjYmZiY2YxNDU3NWFlODNjOGI0NjNkZjJlNjNkZDVjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:13:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IjBNTVE4TnVnOENuZmdnNWJscWliS3c9PSIsInZhbHVlIjoiVkU3MkdOejc1OTlOMHRjT0RET1h2YzdSTTRlbkF6bVZPSEtneXJCdDdFcDRhbVR1UjFFc3ZzZHFKWXdjUWgyWWh1d2VUN1Z1Y2g3UUFLMzEzamo2TG9MK3I4SXlLMUQyVXR6MklTdEZSaW8zVWcvRjVhT1FPNjhUQ05VaUZiR3Y3YnlXb1duQ2ExZUpnOFBqUlhLeWg5WTlCVXlBSnhoNTF4RjJwWFFLc3NXcG5qcEhYVHd6UGJDdyszQXU5ajljNi9EZXdzaWZWdEFWMGN6OWQ2MFFLSnVEaUVLTTZPU2JRQmVFTm9VWnV4Vy8xeDZuQkFleHI0UmlRWHhPMCsrQ25oSzlkazNoTGpFeTY4VXJEOGVYa3B4Qi85M2JORktPWmtMQ0t3RFBzbC8zYXRzQmpLaU5lYWxKT3BvZTJwSVdoeHhNNVk5WHNraHRPUXNJSXVmS28weFNpMWsxdlAvVE9TNkpYVFRRZTl4aGk4OXR0YkZmbnVORlh2cG91ZzdkOUdPeW9YWGUycXBUY2JnZHQxbysweU9BbTJWTGs3dythTGoxWmhYR0IzckF6clhBQ0ZVRytCbHNhdWNCQis3S1kyeHpmcCtjSHlZQW1YVlBMTUxHWmlFK2N4bi8xZlczZE9hWDBFWEk2clNVQ29pUEZHR2lmRlBtWXRJNm12ZFUiLCJtYWMiOiIxNjEwOTdmNzM4N2Y1OGNiYmQ1M2U4Mzc3OWMyNDRkMzVlZDllYTRhYjk0NmFlM2RhMTc2NjhmMTRiOTMxMTZkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:13:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031576245\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-315108578 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315108578\", {\"maxDepth\":0})</script>\n"}}