@php
    $settings = Utility::settings();
    $logo = \App\Models\Utility::get_file('uploads/logo');
    $company_logo = $settings['company_logo_dark'] ?? '';
@endphp
    <!DOCTYPE html>
<html lang="en" dir="{{$settings == 'on'?'rtl':''}}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="{{ asset('assets/css/plugins/main.css') }}">

    <link rel="stylesheet" href="{{ asset('assets/css/plugins/style.css') }}">

    <!-- font css -->
    <link rel="stylesheet" href="{{ asset('assets/fonts/tabler-icons.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/feather.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/fontawesome.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/fonts/material.css') }}">

    <!-- vendor css -->
    <link rel="stylesheet" href="{{ asset('assets/css/style.css') }}" id="main-style-link">

    <title>{{env('APP_NAME')}} - POS Barcode</title>
    @if (isset($settings['SITE_RTL'] ) && $settings['SITE_RTL'] == 'on')
        <link rel="stylesheet" href="{{ asset('assets/css/style-rtl.css')}}" id="main-style-link">
    @endif
    <style>
        .barcode-container {
            border: 1px solid #000;
            padding: 10px;
            margin: 5px;
            text-align: center;
            display: inline-block;
            width: 200px;
            height: 150px;
        }
        .product-name {
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
            text-align: center;
        }
        .product-price {
            font-size: 12px;
            margin-top: 3px;
            text-align: center;
        }
        .company-logo {
            max-width: 80px;
            max-height: 25px;
            margin-bottom: 3px;
        }
    </style>
</head>
<body>
<div id="bot" class="mt-5">
    <div class="row">
        @foreach($productServices as $product)
            @for($i=1;$i<=$quantity;$i++)
                @php
                    // Calculate price with tax
                    $price = $product->sale_price;
                    $taxRate = !empty($product->tax_id) ? $product->taxRate($product->tax_id) : 0;
                    $taxAmount = ($taxRate / 100) * $price;
                    $priceWithTax = $price + $taxAmount;
                    $formattedPrice = \Auth::user()->priceFormat($priceWithTax);
                    $logoPath = $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png');
                @endphp
                <div class="col-auto">
                    <div class="barcode-container">
                        <!-- Company Logo -->
                        <img src="{{ $logoPath }}" alt="Company Logo" class="company-logo">
                        <!-- Barcode -->
                        <div data-id="{{$product->id}}" class="product_barcode product_barcode_hight_de product_barcode_{{$product->id}}" data-skucode="{{ $product->sku }}"></div>
                        <!-- Product Name -->
                        <div class="product-name">{{$product->name}}</div>
                        <!-- Product Price -->
                        <div class="product-price">{{$formattedPrice}}</div>
                    </div>
                </div>
            @endfor
        @endforeach
    </div>
</div>
<script>
    window.print();
    window.onafterprint = back;

    function back() {
        window.close();
        window.history.back();
    }
</script>
<script src="{{ asset('js/jquery.min.js') }}"></script>
<script src="{{ asset('public/js/jquery-barcode.min.js') }}"></script>
<script src="{{ asset('public/js/jquery-barcode.js') }}"></script>
<script>
    $(document).ready(function() {
        $(".product_barcode").each(function() {
            var id = $(this).data("id");
            var sku = $(this).data('skucode');
            sku = encodeURIComponent(sku);
            generateBarcode(sku, id);
        });
    });
    function generateBarcode(val, id) {
        var value = val;
        var btype = '{{ $barcode['barcodeType'] }}';
        var renderer = '{{ $barcode['barcodeFormat'] }}';
        var settings = {
            output: renderer,
            bgColor: '#FFFFFF',
            color: '#000000',
            barWidth: '1',
            barHeight: '50',
            moduleSize: '5',
            posX: '10',
            posY: '20',
            addQuietZone: '1'
        };
        $('.product_barcode_' + id).html("").show().barcode(value, btype, settings);

    }
</script>
</body>
</html>
