@extends('layouts.admin')
@section('page-title')
    {{__('Return Details')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('pos.returns.report')}}">{{__('POS Returns')}}</a></li>
    <li class="breadcrumb-item">{{ AUth::user()->posNumberFormat($return->id) }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('pos.pdf', Crypt::encrypt($return->pos_id))}}" class="btn btn-primary" target="_blank" >{{__('Download Original Invoice')}}</a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mt-2">
                        <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12">
                            <h4>{{__('Return Details')}}</h4>
                        </div>
                        <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12 text-end">
                            <h4 class="invoice-number">{{ Auth::user()->posNumberFormat($return->id) }}</h4>
                        </div>
                        <div class="col-12">
                            <hr>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5">
                            <small class="font-style">
                                <strong>{{__('Billed To')}} :</strong><br>
                                @if(!empty($return->pos->customer->billing_name))
                                    {{!empty($return->pos->customer->billing_name)?$return->pos->customer->billing_name:''}}<br>
                                    {{!empty($return->pos->customer->billing_address)?$return->pos->customer->billing_address:''}}<br>
                                    {{!empty($return->pos->customer->billing_city)?$return->pos->customer->billing_city:'' .', '}}<br>
                                    {{!empty($return->pos->customer->billing_state)?$return->pos->customer->billing_state:'',', '}},
                                    {{!empty($return->pos->customer->billing_zip)?$return->pos->customer->billing_zip:''}}<br>
                                    {{!empty($return->pos->customer->billing_country)?$return->pos->customer->billing_country:''}}<br>
                                    {{!empty($return->pos->customer->billing_phone)?$return->pos->customer->billing_phone:''}}<br>
                                @else
                                    {{__('Walk-in Customer')}}
                                @endif
                            </small>
                        </div>
                        <div class="col-4">
                            <small>
                                <strong>{{__('Original POS')}} :</strong><br>
                                {{ Auth::user()->posNumberFormat($return->pos_id) }}<br>
                                <strong>{{__('Warehouse')}} :</strong><br>
                                {{ !empty($return->pos->warehouse) ? $return->pos->warehouse->name : '' }}<br>
                            </small>
                        </div>
                        <div class="col-3">
                            <div class="d-flex align-items-center justify-content-end">
                                <div class="me-4">
                                    <small>
                                        <strong>{{__('Return Date')}} :</strong>
                                        {{\Auth::user()->dateFormat($return->return_date)}}<br><br>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="table-responsive mt-3">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th class="text-dark" >#</th>
                                        <th class="text-dark">{{__('Items')}}</th>
                                        <th class="text-dark">{{__('Quantity Returned')}}</th>
                                        <th class="text-dark">{{__('Price')}}</th>
                                        <th class="text-dark">{{__('Tax')}}</th>
                                        <th class="text-dark">{{__('Tax Amount')}}</th>
                                        <th class="text-dark">{{__('Total')}}</th>
                                        <th class="text-dark">{{__('Return Reason')}}</th>
                                    </tr>
                                    </thead>
                                    @php
                                        $totalQuantity = 0;
                                        $totalRate = 0;
                                        $totalTaxPrice = 0;
                                        $totalAmount = 0;
                                        $taxesData = [];
                                    @endphp
                                    @foreach($return->items as $key => $item)
                                        @php
                                            $totalQuantity += $item->quantity;
                                            $totalRate += $item->price;
                                            
                                            // Calculate tax
                                            $itemTaxes = [];
                                            $itemTaxAmount = 0;
                                            
                                            if (!empty($item->tax)) {
                                                $taxes = \App\Models\Utility::tax($item->tax);
                                                foreach ($taxes as $tax) {
                                                    $taxRate = $tax->rate;
                                                    $taxPrice = ($item->price * $item->quantity) * ($taxRate / 100);
                                                    $itemTaxAmount += $taxPrice;
                                                    
                                                    // Add to taxes data for display
                                                    $itemTaxes[] = $tax->name . ' (' . $tax->rate . '%)';
                                                    
                                                    // Add to total tax collection
                                                    if (array_key_exists($tax->name, $taxesData)) {
                                                        $taxesData[$tax->name] += $taxPrice;
                                                    } else {
                                                        $taxesData[$tax->name] = $taxPrice;
                                                    }
                                                }
                                            }
                                            
                                            $totalTaxPrice += $itemTaxAmount;
                                            $itemTotal = ($item->price * $item->quantity) + $itemTaxAmount;
                                            $totalAmount += $itemTotal;
                                        @endphp
                                        <tr>
                                            <td>{{$key+1}}</td>
                                            <td>{{!empty($item->product)?$item->product->name:''}}</td>
                                            <td>{{$item->quantity}}</td>
                                            <td>{{\Auth::user()->priceFormat($item->price)}}</td>
                                            <td>
                                                @if(!empty($itemTaxes))
                                                    @foreach($itemTaxes as $tax)
                                                        <span class="badge bg-primary">{{$tax}}</span><br>
                                                    @endforeach
                                                @else
                                                    -
                                                @endif
                                            </td>
                                            <td>{{\Auth::user()->priceFormat($itemTaxAmount)}}</td>
                                            <td>{{\Auth::user()->priceFormat($itemTotal)}}</td>
                                            <td>{{$item->return_reason ?: '-'}}</td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td colspan="6" class="text-end"><strong>{{__('Sub Total')}}:</strong></td>
                                        <td>{{\Auth::user()->priceFormat($totalAmount - $totalTaxPrice)}}</td>
                                        <td></td>
                                    </tr>
                                    @foreach($taxesData as $taxName => $taxPrice)
                                        <tr>
                                            <td colspan="6" class="text-end"><strong>{{$taxName}}:</strong></td>
                                            <td>{{\Auth::user()->priceFormat($taxPrice)}}</td>
                                            <td></td>
                                        </tr>
                                    @endforeach
                                    <tr>
                                        <td colspan="6" class="text-end"><strong>{{__('Total')}}:</strong></td>
                                        <td>{{\Auth::user()->priceFormat($totalAmount)}}</td>
                                        <td></td>
                                    </tr>
                                </table>
                            </div>
                            @if($return->return_note)
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="return_note">{{__('Return Note')}}</label>
                                            <p>{{ $return->return_note }}</p>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
