{"__meta": {"id": "X8336991af61c18713f7db312499467a3", "datetime": "2025-06-17 12:19:21", "utime": **********.918673, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.31094, "end": **********.918697, "duration": 0.6077570915222168, "duration_str": "608ms", "measures": [{"label": "Booting", "start": **********.31094, "relative_start": 0, "end": **********.817534, "relative_end": **********.817534, "duration": 0.5065939426422119, "duration_str": "507ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817548, "relative_start": 0.5066080093383789, "end": **********.9187, "relative_end": 2.86102294921875e-06, "duration": 0.10115194320678711, "duration_str": "101ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0285, "accumulated_duration_str": "28.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.858714, "duration": 0.02633, "duration_str": "26.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 92.386}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.898004, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 92.386, "width_percent": 4.421}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9077759, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 96.807, "width_percent": 3.193}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-183420718 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-183420718\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1350188270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1350188270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2067345827 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2067345827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-159746268 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162725167%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1VVDJrdGZqUWJFRkZyeVYrd242VHc9PSIsInZhbHVlIjoiaVorQVNnSDRDaFhGWUVPZkx4allZODJMRG5vOHA2Sk1vdHpHZTBMbnFyWnA0MVJBT0NmMDduSTRDQ081c0ZWd1hWZUc3aXRKV2I0aWN2MmtIRWZCN3piLzJWU1hlckxWQ0tzMmdCR1ZPMHRXL3JtMSsvSktoTWllSXhvc2ZEeFJMbHFZajB0Sk82SWpkZ1RSNHRYaGowYmdROWl6OGxFeSswZGFuM2RJSG15SXNSc3N1ZmxPQksrQjIzNmZ3UEtpbGNOeFhMVk81cWoya2hGNTJnOU0xTzNyUzV2ejJwRGxQb3NrVm5NdHRoWjV2REFxUTU5MHdZVjlYMkVvS25xSDFyM2J3QWhJTXpqNXdRL3ppazgxY3BKUzBlUFdZMlAzRStMbHROdHU2T1Fpd0JVRGJBQTJoeEUzOHBrS3E5MU5uR1duMytxd3UwalRieDRscUpaTGIyRlY2TlNUWWd1SU9DQUVFemE3ZFRSU3ljSlU2QWtoVjArcDJ4NEwxRVZHbFR4eWpiQ3czRzcwYW1lVFVwdWlCbWx2LytnZXRkVm9Xd1FFMFE1SS9HNC80ZU5mbE93YXRYajh4anpKRE9xaWxFZWw5TFQ1QzVkd3hRclByYmRCSUw5NmV6VDVnamo3T1VZbG0rVW5saGg0T1BrR25jYmwyeW5yM1BNMU15bGEiLCJtYWMiOiJjZDVkMjlkOTdjNzExYTA4MmQ1N2I5YzA4Yzc0OTgwNzM1ZjA4ZGQ0N2M4ZTE5NGY3YzJhM2YzYWVlNDkzNmU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZNRXQvTWRLY3g1UjdrazlybDJSSFE9PSIsInZhbHVlIjoiNjJQOUhFZEdTU2F4T1RwTExQRXFCelpDelJzTmZFaFRuU3NxcVRMbHA3RUpDMm1uUEpBMGpBQ3J1WGNTNzVGL1FUd0dPTlZPa3ltcytQZlFRd25ERVI4YVlPR0tOS0Y0WURVL0ZQbmMyR2JBNkg0OHZPcTZscEdtek1qdm5oRTUrakxzYnNtcEhEb2VVNEJsajh6NXJNTE92aDFLbTBpMDkwajV1bGxqUHl1WVZVeXZuTzdnb2xqN0dyNFVieHdDUzJ5YkFMa2N5VU5icGMybmkyaC9KdjdzbVZSR0wyNEMwVy91MU9hR0swVlEyVTdCTnlwbUMxU1M3V2pJaG5WWk9LVStzQndZOGZSczJVN2IyTUpad1lWR3lUZFhQUGZ3UDdrUzJoNk1yblFIWlUwWHVNa0srSjNzUWtNczRpaHFlNDZ3U1h0d3NhR1p0blBXQ1lYOUpKSkFYTDJPczFJMjVNR3N4ZWdjc2dZZjlvckd0WHpvcllkMFlDVHFQWXhrdGVWM2RHTmY0RXliblFXUXNjaGhIbzQvUVFBK1JoY0l3djNCclE3Wmo4MFhDZjJqV3dZaC9xSUtiQTVXbXp1Mmp3SWNJa2lPVmxReVhHWk03eFN5Ni9EbmVJQzFHb0RORGRmSk9yVDlsYldwMkhCVmcreWpxM0lyS2QzWERyVXciLCJtYWMiOiIzOTQ5NTY0YTkwOTZmZmU4OTAwYzVmOGY2ZDNlNTdiMmE2Y2ZmODU0MWEyNGM1NjRhMTZjMWM2ZDExMGI2YjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-159746268\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-965173675 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-965173675\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-475379007 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklzQ3c3eWZXNUh5TkVjYnNvdE1KaHc9PSIsInZhbHVlIjoiN2gyS1V5eXNFditLYXBJU0VEV0ZNajRUcWZXMThHczhraG9xTjZkZzBycnI2SWx0bDR5NDNja3hkU3FqNEw1S1Raak02Z0RwVkZBNmI2QkIxOTBHZDNtZnNOeDJVK24wVU12cnc4VVpETWJid2NBUWhuUzhOTHRyVUhIS2hjZzZ6OWN5cVl4eHhIcDJEWUV4Y01vVHVYaWFhMytHRVlqeEZXaU1UbEhFQTUvdC9GejZmZDhqUmh3eHJoTzNlUE9URmlFb09qNHROaU9NVFhNeHE3QnlsaFErS3pOc1hsbFhTdUtWQmVGdThjMGljendndm84TkE0akxCTkJTaGs4UURmZ28zc0hsbzR5QjR1SHdqRGJ1UWRkb09oc3VlUGY0eVRHNFlHbU44a2VwWU4wSVY2aU1MV3pndjdTMkJ2aHdJaVRBajJDWjZBRWJsQmdHb0R1czh3YTRQU0h0M2J2MmE1b0xuMFdWVGQzdGUzS2syaisxNzVJMVRKVTByNkJIN1NUT2Z2QzkrRm1hWUlzWU1UdnVyYVBTUW1BSjE1ZGFwU3IxYnh6c2lSZkp4VXJOam1sMDhpY3REby80cXNaZFJrU1VyR3pVTXRhTW1EQ2hQb1BPZVRPdmpUZkx0SGR6c3BBelc0eHBxTjB3L1lEK0JFcEFEc05nSmlaU2pXV0UiLCJtYWMiOiI3YjFlMTA0NzY2Mjg0MGJmNjRhOWVhNDVmNDcxYzgyNjZkYjY1MTE5NWMzOGNkZGI2MGExMWRlYjZjYmZlODFlIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlMrMktSVlNlMFRxelVKSVMxdTJLb1E9PSIsInZhbHVlIjoiUlpGWVNIY0VCQlFiTkFMa3dtWlQxSDBNYzRjOVp0a0h1YUplRFBjQ2VMc05jc1ArcFlaRUFWVFBNcmIzNEx1MVkybVR1L2tYVVJ3UXFQOU9WMkMzN3ZsRERvQWFhQ3J1KzZwZUdtZU15RlJEUityYzNLZnU5MGtYcm5lZ2N5dWNIMjdnajJhKzA5b1QyN1RFcVZXMDVDdTd0ZmFqTjE4b2c4N1cwdkdJUEViWWg0V1V5eTd2aWhNQ1ZGaGx2Y3VwU215aVJYRWRTRnRrNDF2RFlsZzN1NHNKdUMwWFg4YmVzbm5pbW1QNVVtZTE0RkhrdE5wRUVaVzlSaXptbXlodU8zeDl4MytUSzlLTkJWUVBnWVgzdHNtWlljOFNNT0c2Z1gyaTdUQ1RGSGJINlpPQXExNHVxQU1TdzBuQklSUEJlaXArbVZ2aWdkNFhjdG1uUTJmb3hDdHk1NW9tdzkxRGVrUG9XeFY1QUZWWVVLcnBoU2xjblk0V0JrSGtWeVpZQmpYanRkZE5HeVMxUG9JRUdlNnBvVGF3ME1aTnpIL09odGdsQmp5aUNjVlRuaWt1RTZnLzlvNDV5ZllYQTJKZ2RTTGdkMXhMd1hUT2p4UzZGaWI0cEM1VmV0b2daWCt0UWZ4NzdVQXhobTRWZ1BnbUJZL3dZemJWNk5naUZsQUYiLCJtYWMiOiJjNDA2NGY0YWQzNDA0MWNjMzc2NjA2Mzc1ZTdiMmFmODExMjFhM2FlY2VjZTQ5NzhmODA3M2M2NzA1OWFhYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklzQ3c3eWZXNUh5TkVjYnNvdE1KaHc9PSIsInZhbHVlIjoiN2gyS1V5eXNFditLYXBJU0VEV0ZNajRUcWZXMThHczhraG9xTjZkZzBycnI2SWx0bDR5NDNja3hkU3FqNEw1S1Raak02Z0RwVkZBNmI2QkIxOTBHZDNtZnNOeDJVK24wVU12cnc4VVpETWJid2NBUWhuUzhOTHRyVUhIS2hjZzZ6OWN5cVl4eHhIcDJEWUV4Y01vVHVYaWFhMytHRVlqeEZXaU1UbEhFQTUvdC9GejZmZDhqUmh3eHJoTzNlUE9URmlFb09qNHROaU9NVFhNeHE3QnlsaFErS3pOc1hsbFhTdUtWQmVGdThjMGljendndm84TkE0akxCTkJTaGs4UURmZ28zc0hsbzR5QjR1SHdqRGJ1UWRkb09oc3VlUGY0eVRHNFlHbU44a2VwWU4wSVY2aU1MV3pndjdTMkJ2aHdJaVRBajJDWjZBRWJsQmdHb0R1czh3YTRQU0h0M2J2MmE1b0xuMFdWVGQzdGUzS2syaisxNzVJMVRKVTByNkJIN1NUT2Z2QzkrRm1hWUlzWU1UdnVyYVBTUW1BSjE1ZGFwU3IxYnh6c2lSZkp4VXJOam1sMDhpY3REby80cXNaZFJrU1VyR3pVTXRhTW1EQ2hQb1BPZVRPdmpUZkx0SGR6c3BBelc0eHBxTjB3L1lEK0JFcEFEc05nSmlaU2pXV0UiLCJtYWMiOiI3YjFlMTA0NzY2Mjg0MGJmNjRhOWVhNDVmNDcxYzgyNjZkYjY1MTE5NWMzOGNkZGI2MGExMWRlYjZjYmZlODFlIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlMrMktSVlNlMFRxelVKSVMxdTJLb1E9PSIsInZhbHVlIjoiUlpGWVNIY0VCQlFiTkFMa3dtWlQxSDBNYzRjOVp0a0h1YUplRFBjQ2VMc05jc1ArcFlaRUFWVFBNcmIzNEx1MVkybVR1L2tYVVJ3UXFQOU9WMkMzN3ZsRERvQWFhQ3J1KzZwZUdtZU15RlJEUityYzNLZnU5MGtYcm5lZ2N5dWNIMjdnajJhKzA5b1QyN1RFcVZXMDVDdTd0ZmFqTjE4b2c4N1cwdkdJUEViWWg0V1V5eTd2aWhNQ1ZGaGx2Y3VwU215aVJYRWRTRnRrNDF2RFlsZzN1NHNKdUMwWFg4YmVzbm5pbW1QNVVtZTE0RkhrdE5wRUVaVzlSaXptbXlodU8zeDl4MytUSzlLTkJWUVBnWVgzdHNtWlljOFNNT0c2Z1gyaTdUQ1RGSGJINlpPQXExNHVxQU1TdzBuQklSUEJlaXArbVZ2aWdkNFhjdG1uUTJmb3hDdHk1NW9tdzkxRGVrUG9XeFY1QUZWWVVLcnBoU2xjblk0V0JrSGtWeVpZQmpYanRkZE5HeVMxUG9JRUdlNnBvVGF3ME1aTnpIL09odGdsQmp5aUNjVlRuaWt1RTZnLzlvNDV5ZllYQTJKZ2RTTGdkMXhMd1hUT2p4UzZGaWI0cEM1VmV0b2daWCt0UWZ4NzdVQXhobTRWZ1BnbUJZL3dZemJWNk5naUZsQUYiLCJtYWMiOiJjNDA2NGY0YWQzNDA0MWNjMzc2NjA2Mzc1ZTdiMmFmODExMjFhM2FlY2VjZTQ5NzhmODA3M2M2NzA1OWFhYjkwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-475379007\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}