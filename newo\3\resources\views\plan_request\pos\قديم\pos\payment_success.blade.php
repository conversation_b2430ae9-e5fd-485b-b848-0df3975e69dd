@php
    $settings = Utility::settings();
    $pos = \App\Models\Pos::find($pos_id);
    $pos_number = $pos ? \Auth::user()->posNumberFormat($pos->pos_id) : 'N/A';
@endphp

<div class="container-fluid p-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Success Header -->
            <div class="alert alert-success text-center mb-4" style="border-radius: 15px; border: none; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <i class="ti ti-check-circle" style="font-size: 3em; color: #28a745; margin-right: 15px;"></i>
                    <div>
                        <h3 class="mb-0 text-success">{{ __('Payment Completed Successfully!') }}</h3>
                        <p class="mb-0 text-muted">{{ __('Your transaction has been processed') }}</p>
                    </div>
                </div>
            </div>

            <!-- Invoice Details Card -->
            <div class="card shadow-sm mb-4" style="border-radius: 15px; border: none;">
                <div class="card-header bg-primary text-white" style="border-radius: 15px 15px 0 0;">
                    <h5 class="mb-0"><i class="ti ti-file-invoice me-2"></i>{{ __('Invoice Details') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>{{ __('Invoice Number') }}:</strong> <span class="text-primary">{{ $pos_number }}</span></p>
                            <p><strong>{{ __('Payment Type') }}:</strong> 
                                @if($payment_type == 'cash')
                                    <span class="badge bg-success">💵 {{ __('Cash') }}</span>
                                @elseif($payment_type == 'network')
                                    <span class="badge bg-info">💳 {{ __('Network') }}</span>
                                @elseif($payment_type == 'split')
                                    <span class="badge bg-warning">💰💳 {{ __('Split Payment') }}</span>
                                @endif
                            </p>
                            <p><strong>{{ __('Total Amount') }}:</strong> <span class="text-success h5">{{ \Auth::user()->priceFormat($total_amount) }}</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>{{ __('Date') }}:</strong> {{ date('Y-m-d H:i:s') }}</p>
                            <p><strong>{{ __('Status') }}:</strong> <span class="badge bg-success">{{ __('Paid') }}</span></p>
                            @if($payment_type == 'network' && isset($payment_data['transaction_number']))
                                <p><strong>{{ __('Transaction Number') }}:</strong> {{ $payment_data['transaction_number'] }}</p>
                            @endif
                        </div>
                    </div>

                    @if($payment_type == 'split')
                        <hr>
                        <h6>{{ __('Split Payment Details') }}:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>{{ __('Cash Amount') }}:</strong> {{ \Auth::user()->priceFormat($payment_data['split_cash_amount'] ?? 0) }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>{{ __('Network Amount') }}:</strong> {{ \Auth::user()->priceFormat($payment_data['split_network_amount'] ?? 0) }}</p>
                                @if(isset($payment_data['split_transaction_number']))
                                    <p><strong>{{ __('Network Transaction') }}:</strong> {{ $payment_data['split_transaction_number'] }}</p>
                                @endif
                            </div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="card shadow-sm" style="border-radius: 15px; border: none;">
                <div class="card-header bg-light" style="border-radius: 15px 15px 0 0;">
                    <h5 class="mb-0"><i class="ti ti-settings me-2"></i>{{ __('What would you like to do next?') }}</h5>
                </div>
                <div class="card-body text-center">
                    <div class="btn-group-vertical btn-group-lg" role="group" style="width: 100%; max-width: 400px; margin: 0 auto;">
                        <!-- Thermal Print Button -->
                        <a href="{{ route('pos.thermal.print', $pos_id) }}" target="_blank" 
                           class="btn btn-success btn-lg mb-3 thermal-print-btn" 
                           style="border-radius: 10px; padding: 15px;">
                            <i class="ti ti-device-mobile me-2"></i>
                            <strong>🖨️ {{ __('Print Thermal Receipt') }}</strong>
                            <br><small>{{ __('Open thermal printer window') }}</small>
                        </a>

                        <!-- Print Preview Button -->
                        <button type="button" class="btn btn-info btn-lg mb-3" 
                                onclick="printInvoice()" 
                                style="border-radius: 10px; padding: 15px;">
                            <i class="ti ti-printer me-2"></i>
                            <strong>📄 {{ __('Print Preview') }}</strong>
                            <br><small>{{ __('Standard printer preview') }}</small>
                        </button>

                        <!-- View Invoice Button -->
                        <a href="{{ route('pos.show', \Illuminate\Support\Facades\Crypt::encrypt($pos_id)) }}" 
                           target="_blank" class="btn btn-secondary btn-lg mb-3" 
                           style="border-radius: 10px; padding: 15px;">
                            <i class="ti ti-eye me-2"></i>
                            <strong>👁️ {{ __('View Invoice') }}</strong>
                            <br><small>{{ __('View full invoice details') }}</small>
                        </a>

                        <!-- New Sale Button -->
                        <button type="button" class="btn btn-primary btn-lg" 
                                onclick="startNewSale()" 
                                style="border-radius: 10px; padding: 15px;">
                            <i class="ti ti-refresh me-2"></i>
                            <strong>🔄 {{ __('Start New Sale') }}</strong>
                            <br><small>{{ __('Begin a new transaction') }}</small>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Auto Close Notice -->
            <div class="alert alert-info text-center mt-4" id="autoCloseNotice" style="border-radius: 10px;">
                <i class="ti ti-info-circle me-2"></i>
                <span id="autoCloseText">{{ __('This window will automatically close in') }} <strong id="countdown">10</strong> {{ __('seconds') }}</span>
                <button type="button" class="btn btn-sm btn-outline-info ms-3" onclick="cancelAutoClose()">
                    {{ __('Cancel Auto Close') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Auto close countdown
    let countdown = 10;
    let autoCloseInterval;
    let autoCloseEnabled = true;

    function startCountdown() {
        autoCloseInterval = setInterval(function() {
            countdown--;
            document.getElementById('countdown').textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(autoCloseInterval);
                if (autoCloseEnabled) {
                    startNewSale();
                }
            }
        }, 1000);
    }

    function cancelAutoClose() {
        autoCloseEnabled = false;
        clearInterval(autoCloseInterval);
        document.getElementById('autoCloseNotice').style.display = 'none';
    }

    function startNewSale() {
        // إفراغ السلة وإعادة تحميل الصفحة
        window.location.href = '{{ route("poses.index") }}';
    }

    function printInvoice() {
        // فتح نافذة طباعة عادية
        window.open('{{ route("pos.show", \Illuminate\Support\Facades\Crypt::encrypt($pos_id)) }}', '_blank');
    }

    // إضافة تأثير نبضة لزر الطباعة الحرارية
    document.addEventListener('DOMContentLoaded', function() {
        const thermalBtn = document.querySelector('.thermal-print-btn');
        if (thermalBtn) {
            thermalBtn.classList.add('pulse-animation');
        }
        
        // بدء العد التنازلي
        startCountdown();
    });

    // معالج النقر لزر الطباعة الحرارية
    document.addEventListener('click', function(e) {
        if (e.target.closest('.thermal-print-btn')) {
            e.preventDefault();
            const thermalUrl = e.target.closest('.thermal-print-btn').href;
            
            if (confirm('{{ __("Do you want to print the thermal receipt?") }}')) {
                const printWindow = window.open(thermalUrl, '_blank', 'width=450,height=850,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no');
                
                if (printWindow) {
                    show_toastr('success', '{{ __("Thermal print window opened successfully") }}', 'success');
                    printWindow.focus();
                } else {
                    show_toastr('error', '{{ __("Failed to open print window. Please allow pop-ups.") }}', 'error');
                }
            }
        }
    });
</script>

<style>
    .pulse-animation {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }
        70% {
            transform: scale(1.05);
            box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
        }
        100% {
            transform: scale(1);
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
        }
    }
    
    .btn:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }
    
    .card {
        transition: all 0.3s ease;
    }
    
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
    }
</style>
