{"__meta": {"id": "Xe8d03c919572ba3c7a02e89c82d5c5fc", "datetime": "2025-06-17 12:19:44", "utime": **********.343029, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162783.764197, "end": **********.343052, "duration": 0.5788547992706299, "duration_str": "579ms", "measures": [{"label": "Booting", "start": 1750162783.764197, "relative_start": 0, "end": **********.256322, "relative_end": **********.256322, "duration": 0.4921247959136963, "duration_str": "492ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.256339, "relative_start": 0.4921419620513916, "end": **********.343055, "relative_end": 3.0994415283203125e-06, "duration": 0.0867159366607666, "duration_str": "86.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45177664, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018130000000000004, "accumulated_duration_str": "18.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2944329, "duration": 0.01616, "duration_str": "16.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.134}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3223739, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.134, "width_percent": 5.902}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.331171, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 95.036, "width_percent": 4.964}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1549942174 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1549942174\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-213709371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-213709371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1140341732 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140341732\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-487154159 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162773418%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9XaU9Ua0lWRXFmZUJOVkRsZlErS0E9PSIsInZhbHVlIjoiWXY0aDJxZFJjZnZMUUtCK2R1LzlsS2pDS1RNZGNLcURJOHlrQzBCR0U2WmpNYzZkN1A2M1YzSTRiVDZhVzRDNGdGemtuTGtsTkJVRXlsdEJJOWw1aG00NGxuQkUyQWQxQWp4QVpBVmlkRG1pakFTNWY1R3NtcWpBZk9HSEFqNHZ0akorRDhRc1lORERFSkVLNWxTc1BQWEZWS0RqZ2dha1dqZ0JrN2MyT2dWSGNMV1hPZ01CYmJWazVvcWFCTEpZdXBsdkpWejhaUXFqQjdGZHlQdVM0OGtaRGVLRXh5eERsZWMwZ2tQWVoxMGgrTHlCbjFQcEZQWHppbXhBNWZQZ0h5anhpWkp3aG93QTNSdnB0YXcva0ExUXRDNTJTbTQxdEowM0MrcVpPNXJiMWlGclZLTnhDR2ErTXVFR1kzeS9oaW4wL1ZCQnpqRk5DRUNLUGNaWjlIQmRid1pCT0d0c3JzK2FNbXBQZTdYdFhxYTRvMnppN3JKbStlUHJjWE1ETnVuNHlubk1TYkdDRzJWUE9wZGVBZUxNZDZFbCtFWEVKRlB3SEZJM1cyWjVIYUN3VFBOV0RWeU1QS1FqR0tpNDIyeWZxcGdRbmtJTTdmbUZMbzhmNlp3QkMxL2xqRVpzMVFpZUk2NVVpSjVFTUI2ZG5KTUlVQjRQNkZ1WW9pVWEiLCJtYWMiOiI2N2Y1YTRlYTk3ZjkzNDlhNTEwZmEzYzRjZTVlNTAxZTMwNmE3NjcxNTc1MWJlNGRkMmMyODA4ZmEwYjA4ODYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFwYVJoMnNwL2RRSE5uRUhpSHNabFE9PSIsInZhbHVlIjoiK0ZWVmJuMzduaTQ1SDE2K0xoblo0dXJYWDJQRHpIMm1KTHlYRlRMOU1vcFRRRTFsUXpibEd1Zi9HYVNGMk5wS1R0bU4rMGNxZmlOTGIzTktNd0NDOXdwRytjVDhMNlhvZExwQ1ZVaFR0T2NydmZYc2xpSzRXUGFBcnIwdEh2VFkwNktXeUFCdjlrS3VJclloQk5rMGpsL1hZQW5rSUM0dUg4dzVDQ2JyY0pBNjhIeFVBUDgxdEc5T0Nyb3gyMTFxdVBGRmh3R2Nab3BodDlKdXpaL2lvdWRxOThvU3IvaW5QUE5QczY5cEJDNzBTSGJrWFh0R1ZNb0VRN0d2Q1IyRGo4end3dFBtaHZvaVpOazgzem5KSVY2TDN1c2VUSmd5cWd1cGt0Y0wwczNvK0R1eHUzaTkrVmlKdXBhc0VPRXhTWk0vcmtXRkxKZ0JNcjBHdkd2YnZoRnFFQ2NCdGZDRE1uMHN3RFZoRzVtYWt5TTVVbGN1Smxqc0xhRFp2YUhDN1hmK0tSdWpKb29RRnB0K1VyUi9GZmc3clhQby9yVlQ4L2pEWTJoRThOUWVYTUsxRXBMRDdsQXRCMEEzWlhTcWo5Uk14T1FSd1hRdE90U1NVSlJROC9vcU9uY1orMjVaNUlURTRBLyt6YytjZTFKZjlxOEVPc3V5aXV2WTU2blAiLCJtYWMiOiIyMjE5MGQwMTUwZTViY2QyMTBjODdiMjhjZjI4ZWRlMTJiMDM1MjUzOTZjMmIxODQ0ZDI5Yzg2MzY1MzQ4MTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-487154159\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1303351874 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1303351874\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1297543480 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndiQVAzZW54aDJnUFRvT1pBdGZPWlE9PSIsInZhbHVlIjoiZlIrRWlEM2FZWldyU28vUWJtWTRiZWNrVGUyck1UODNOSm1uQWtXNmlOR2ZWZHdBaHRLTlRyUWo1V0xUS3VBUFhXR3JwQVQ3TFdKdUxhYU15QUhiZFovYnJZejJ0SVpNZFd5V2FlaVd0SkgzRmx2S1NxWGdJZUdVUlpMdEFsU3JoV0kwVUYzcjVvR1VBTGtoWm10YkthYjJob0t1TWpuUDJQVUpna2RncWo0VVUzb1lBcFh3MzhuMkZIdlI0Y015NkpXOXRXeitEY2hTcTYxQ1FIMUx2Nzh3L0QwVEZoTXZWTzRHWWxXN05JQnAxWTR1K2RobGlOQVJHdnBTQWhrb3NLRjZTeE5xbXhVeUY0K3U2L1hmVEhnY3RROE03MDFYSkFRZTV0Tmd3ZlBBVUF6MXdwMEJ4NThkWVcvSnFNSzFIU3lSQjEzZE1qZzB4eGg1WVVIOTNpSUVzOHdJNk03UmlmN3U4cWJWV0lUcGl5U0trU2VvOFAvTjcrQTNNNG1xSVpZYXp0a01pYThwOFdvSE0rdUtLL29JWkRoeTRidkdSOHlTSkllbWNVcWFuL2dtZGpJY0MreHIxcFZtMURIdDFaN3NuSDgxZjQ5dlh1dEVmb1VXL0pVdFlGVVpJV0J1bXhIM2xpdVgyRXVYbFNRUmhZVEJlbVU0RXZWdSswSFQiLCJtYWMiOiJmZDAxNjdiZTAzYjdkYWNjNjljNGIwNjMzZmFmMmEyYWJhZjE2MWUxOGJkNWEzMjZhYzM2MjE5NGVhOWU1ZmE4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkYxV2ljRXZrM3FzT1A0VVZsZFlYeXc9PSIsInZhbHVlIjoiQWZBZG9zZEEwU1V6d3VzbTlBWTkzWXJic0kwRUV5RlN3K1NObUFIZlR3anRLQnExcXo4YWNjc3lVNkpUM3Q0MXRzOHpmM0RYSWtuN21vbkR0WHdoSnVETVkxbVdrNUlxVVZWdU81L3dUOXZSeFFaWWJTVlRwRUN5WkNBVy9WMGdHcTh5VzdWYWhVamlLMWYxeEN3VnpyV2FubHJHSmN2R2lBMG16RnR2WFFxbnVyYkVTSnlsR0hya3krUE9oaGxqTjMrRkJEMzJjalAvTG5pNXJrSFlRdHpacTVkbXY4c3lTWlBlL21mQm9aQXNpajRVTDYyQVVQeGFWaWJZQW56U2pwbWkwVVR3L1R1TU5MNXVDb0xkMm5aM3lsdTlDVFRuTjEwL25rYk1oekw4MjNHclRPV0taTHZIY3J6WUdUbTBuVk5HZTBrMVB5Ukl5aXBpUmlMemVqWDF2c2IyQjJvUzEzMVZJNzhvbzNyQk15VjlPd1ZBazF1UC9GMXRaWkEwblpHVEthR2k0WHZ3cFlMR2J4SGw0M01GUC9DTEpTQVBvTmRnN0VLWTg4dzljbXo2SnpmTCtxSTg3MlR0c1duYncwWWJ5SUJnc3lKTVNmcGhGT3JmUzg3TklqYlJLM1d6TFpOaHQvTFh0QzNpWWFvckk0OC9tK2NuYWU1ZFNIUE0iLCJtYWMiOiJiOTQ0NDkyMWRhYmU1NWU2Njk2ZDVhYmY3ZjI2NWJlMWRmN2ViMWY1MzA3NjI5Mzk4MDQyYjc2MGU0MWRlZDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndiQVAzZW54aDJnUFRvT1pBdGZPWlE9PSIsInZhbHVlIjoiZlIrRWlEM2FZWldyU28vUWJtWTRiZWNrVGUyck1UODNOSm1uQWtXNmlOR2ZWZHdBaHRLTlRyUWo1V0xUS3VBUFhXR3JwQVQ3TFdKdUxhYU15QUhiZFovYnJZejJ0SVpNZFd5V2FlaVd0SkgzRmx2S1NxWGdJZUdVUlpMdEFsU3JoV0kwVUYzcjVvR1VBTGtoWm10YkthYjJob0t1TWpuUDJQVUpna2RncWo0VVUzb1lBcFh3MzhuMkZIdlI0Y015NkpXOXRXeitEY2hTcTYxQ1FIMUx2Nzh3L0QwVEZoTXZWTzRHWWxXN05JQnAxWTR1K2RobGlOQVJHdnBTQWhrb3NLRjZTeE5xbXhVeUY0K3U2L1hmVEhnY3RROE03MDFYSkFRZTV0Tmd3ZlBBVUF6MXdwMEJ4NThkWVcvSnFNSzFIU3lSQjEzZE1qZzB4eGg1WVVIOTNpSUVzOHdJNk03UmlmN3U4cWJWV0lUcGl5U0trU2VvOFAvTjcrQTNNNG1xSVpZYXp0a01pYThwOFdvSE0rdUtLL29JWkRoeTRidkdSOHlTSkllbWNVcWFuL2dtZGpJY0MreHIxcFZtMURIdDFaN3NuSDgxZjQ5dlh1dEVmb1VXL0pVdFlGVVpJV0J1bXhIM2xpdVgyRXVYbFNRUmhZVEJlbVU0RXZWdSswSFQiLCJtYWMiOiJmZDAxNjdiZTAzYjdkYWNjNjljNGIwNjMzZmFmMmEyYWJhZjE2MWUxOGJkNWEzMjZhYzM2MjE5NGVhOWU1ZmE4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkYxV2ljRXZrM3FzT1A0VVZsZFlYeXc9PSIsInZhbHVlIjoiQWZBZG9zZEEwU1V6d3VzbTlBWTkzWXJic0kwRUV5RlN3K1NObUFIZlR3anRLQnExcXo4YWNjc3lVNkpUM3Q0MXRzOHpmM0RYSWtuN21vbkR0WHdoSnVETVkxbVdrNUlxVVZWdU81L3dUOXZSeFFaWWJTVlRwRUN5WkNBVy9WMGdHcTh5VzdWYWhVamlLMWYxeEN3VnpyV2FubHJHSmN2R2lBMG16RnR2WFFxbnVyYkVTSnlsR0hya3krUE9oaGxqTjMrRkJEMzJjalAvTG5pNXJrSFlRdHpacTVkbXY4c3lTWlBlL21mQm9aQXNpajRVTDYyQVVQeGFWaWJZQW56U2pwbWkwVVR3L1R1TU5MNXVDb0xkMm5aM3lsdTlDVFRuTjEwL25rYk1oekw4MjNHclRPV0taTHZIY3J6WUdUbTBuVk5HZTBrMVB5Ukl5aXBpUmlMemVqWDF2c2IyQjJvUzEzMVZJNzhvbzNyQk15VjlPd1ZBazF1UC9GMXRaWkEwblpHVEthR2k0WHZ3cFlMR2J4SGw0M01GUC9DTEpTQVBvTmRnN0VLWTg4dzljbXo2SnpmTCtxSTg3MlR0c1duYncwWWJ5SUJnc3lKTVNmcGhGT3JmUzg3TklqYlJLM1d6TFpOaHQvTFh0QzNpWWFvckk0OC9tK2NuYWU1ZFNIUE0iLCJtYWMiOiJiOTQ0NDkyMWRhYmU1NWU2Njk2ZDVhYmY3ZjI2NWJlMWRmN2ViMWY1MzA3NjI5Mzk4MDQyYjc2MGU0MWRlZDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297543480\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1853926280 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853926280\", {\"maxDepth\":0})</script>\n"}}