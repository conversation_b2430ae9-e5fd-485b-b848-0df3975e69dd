@php
    $settings = Utility::settings();
    // الحصول على شعار الشركة من إعدادات POS
    $pos_logo = asset(Storage::url('uploads/pos_logo/'));
    $company_logo = !empty($settings['pos_logo']) ? $pos_logo . '/' . $settings['pos_logo'] : '';

    // تحقق من وجود مكتبة QR Code
    $qr_enabled = isset($settings['pos_qr_display']) && $settings['pos_qr_display'] == 'on';
@endphp
<style>
    @media print {
        @page {
            size: 72mm auto; /* عرض 72 مم وارتفاع محدود */
            margin: 0;
            padding: 0;
        }

        body {
            margin: 0;
            padding: 0;
            width: 72mm;
            max-height: 500mm; /* تحديد ارتفاع أقصى معقول */
        }

        .thermal-print {
            width: 72mm;
            font-size: 10px;
            font-family: 'Courier New', monospace;
            line-height: 1.2;
            padding: 5px;
        }

        .logo-container {
            text-align: center;
            margin-bottom: 8px;
        }

        .logo-img {
            max-width: 60mm;
            max-height: 20mm;
        }

        .company-name {
            font-size: 12px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 5px;
        }

        .receipt-info {
            font-size: 10px;
            margin-bottom: 5px;
        }

        .receipt-info div {
            margin-bottom: 2px;
        }

        .section-title {
            font-size: 10px;
            font-weight: bold;
            margin-top: 5px;
            margin-bottom: 5px;
        }

        .item-row {
            display: flex;
            justify-content: space-between;
            padding: 2px 0;
        }

        .item-name {
            font-weight: bold;
            margin-bottom: 2px;
        }

        .item-detail {
            display: flex;
            justify-content: space-between;
            padding: 1px 0;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            font-weight: bold;
            padding: 3px 0;
            border-top: 1px solid #000;
            margin-top: 5px;
        }

        .thank-you {
            text-align: center;
            font-size: 9px;
            margin-top: 10px;
            margin-bottom: 5px;
        }

        .divider {
            border-bottom: 1px dotted #ccc;
            margin: 5px 0;
        }

        .qr-code {
            text-align: center;
            margin-top: 10px;
            margin-bottom: 10px;
            display: block;
            width: 100%;
        }

        .qr-code svg {
            width: 50mm !important;
            height: auto !important;
            margin: 0 auto;
            display: block;
        }
    }
</style>

<div class="thermal-print" id="printarea">
    <!-- شعار الشركة -->
    @if(!empty($company_logo))
    <div class="logo-container">
        <img src="{{$company_logo}}" alt="Company Logo" class="logo-img">
    </div>
    @else
    <div class="company-name">{{$settings['company_name']}}</div>
    @endif

    <div class="divider"></div>

    <!-- معلومات العميل -->
    <div class="receipt-info">
        {!! isset($details['customer']['name']) ? '<div><b>' . __('Customer') . ':</b> ' . $details['customer']['name'] . '</div>' : '' !!}
    </div>

    <!-- التاريخ والوقت -->
    <div class="receipt-info">
        {!! isset($details['date']) ? '<div><b>' . __('Date') . ':</b> ' . $details['date'] . '</div>' : '' !!}
    </div>

    <!-- اسم المستخدم -->
    <div class="receipt-info">
        <div><b>{{__('Cashier')}}:</b> {{ $details['user']['name'] ?? '' }}</div>
    </div>

    <!-- رقم الفاتورة -->
    <div class="receipt-info">
        <div><b>{{__('Invoice No')}}:</b> {{ $details['pos_id'] }}</div>
    </div>

    <div class="divider"></div>

    <!-- المنتجات -->
    <div class="section-title">{{__('Items')}}</div>

    <table style="width: 100%; font-size: 9px; border-collapse: collapse;">
        <tr style="border-bottom: 1px dotted #ccc;">
            <th style="text-align: left; padding: 2px;">{{__('Item')}}</th>
            <th style="text-align: right; padding: 2px;">{{__('Qty')}}</th>
            <th style="text-align: right; padding: 2px;">{{__('Price')}}</th>
            <th style="text-align: right; padding: 2px;">{{__('Total')}}</th>
        </tr>

        @foreach ($sales['data'] as $key => $value)
        <tr style="border-bottom: 1px dotted #ccc;">
            <td style="text-align: left; padding: 2px;">{{ $value['name'] }}</td>
            <td style="text-align: right; padding: 2px;">{{ $value['quantity'] }}</td>
            <td style="text-align: right; padding: 2px;">{{ $value['price'] }}</td>
            <td style="text-align: right; padding: 2px;">{{ $value['subtotal'] }}</td>
        </tr>
        @endforeach
    </table>

    <div class="divider"></div>

    <!-- الإجماليات -->
    <div class="item-detail">
        <div>{{__('Subtotal')}}</div>
        <div>{{ $sales['total'] }}</div>
    </div>

    <div class="item-detail">
        <div>{{__('Tax')}}</div>
        <div>{{ $sales['tax'] ?? '0.00' }}</div>
    </div>

    <div class="item-detail">
        <div>{{__('Discount')}}</div>
        <div>{{ $sales['discount'] }}</div>
    </div>

    <div class="total-row">
        <div>{{__('Total')}}</div>
        <div>{{ $sales['total'] }}</div>
    </div>

    <div class="thank-you">{{__('Thank You For Shopping With Us. Please visit again.')}}</div>

    <!-- رمز QR إذا كان مفعل في الإعدادات -->
    @if($qr_enabled)
    <div class="qr-code">
        @php
            $qr_text = $details['pos_id'] . ' - ' . $settings['company_name'] . ' - ' . $sales['total'];
        @endphp
        {!! DNS2D::getBarcodeHTML($qr_text, "QRCODE", 5, 5) !!}
    </div>
    @endif
</div>

<div class="justify-content-center pt-2 modal-footer">
    <a href="#" id="print" class="btn btn-primary btn-sm text-right float-right mb-3">
        {{ __('Print') }}
    </a>
</div>

<script>
    $("#print").click(function () {
        var print_div = document.getElementById("printarea");
        $('.row').addClass('d-none');
        $('.toast').addClass('d-none');
        $('#print').addClass('d-none');

        // تعيين خيارات الطباعة المناسبة للطابعة الحرارية
        var originalContents = document.body.innerHTML;
        document.body.innerHTML = print_div.innerHTML;

        // تأكد من أن رمز QR مرئي عند الطباعة
        setTimeout(function() {
            window.print();

            document.body.innerHTML = originalContents;
            $('.row').removeClass('d-none');
            $('#print').removeClass('d-none');
            $('.toast').removeClass('d-none');

            // إفراغ السلة بعد الطباعة
            emptyCartAfterPrint();
        }, 500);
    });

    // دالة لإفراغ السلة بعد الطباعة
    function emptyCartAfterPrint() {
        // إرسال طلب AJAX لإفراغ السلة
        $.ajax({
            url: '{{ route("empty-cart") }}',
            type: 'POST',
            headers: {
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            data: {
                session_key: 'pos'
            },
            success: function(response) {
                // عرض رسالة نجاح
                console.log("Cart emptied successfully");
                // إعادة توجيه المستخدم إلى صفحة نقاط البيع الرئيسية بعد تأخير قصير
                setTimeout(function() {
                    window.location.href = '{{ route("poses.index") }}';
                }, 500);
            },
            error: function(xhr, status, error) {
                console.error("Error emptying cart:", error);
                // إعادة توجيه المستخدم إلى صفحة نقاط البيع الرئيسية حتى في حالة الخطأ
                setTimeout(function() {
                    window.location.href = '{{ route("poses.index") }}';
                }, 1000);
            }
        });
    }
</script>
