@extends('layouts.admin')
@section('page-title')
    {{__('POS Returns')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS Returns')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('content')
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <h4>{{__('POS Returns')}}</h4>
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('Return ID')}}</th>
                                    <th>{{__('POS ID')}}</th>
                                    <th>{{ __('Date') }}</th>
                                    <th>{{ __('Customer') }}</th>
                                    <th>{{ __('Action') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posReturns as $return)
                                    <tr>
                                        <td class="Id">
                                            {{ $return->id }}
                                        </td>
                                        <td>
                                            {{ $return->pos_id }}
                                        </td>
                                        <td>{{ $return->return_date }}</td>
                                        <td>{{ !empty($return->pos->customer) ? $return->pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>
                                            <a href="{{ route('pos.return.view', $return->id) }}" class="btn btn-sm btn-primary">
                                                <i class="ti ti-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center text-dark"><p>{{__('No Data Found')}}</p></td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
