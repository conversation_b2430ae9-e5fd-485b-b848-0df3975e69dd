{"__meta": {"id": "Xa62a5a82a858c7fb7b1e94063c4e030c", "datetime": "2025-06-17 12:18:43", "utime": **********.576224, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162722.884791, "end": **********.576247, "duration": 0.6914560794830322, "duration_str": "691ms", "measures": [{"label": "Booting", "start": 1750162722.884791, "relative_start": 0, "end": **********.46353, "relative_end": **********.46353, "duration": 0.5787391662597656, "duration_str": "579ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.46355, "relative_start": 0.5787591934204102, "end": **********.576249, "relative_end": 1.9073486328125e-06, "duration": 0.11269879341125488, "duration_str": "113ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019870000000000002, "accumulated_duration_str": "19.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5206928, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.727}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.552961, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.727, "width_percent": 5.838}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.563586, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.565, "width_percent": 5.435}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-873867889 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-873867889\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1218366497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1218366497\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920921504 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920921504\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-660420110 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRsRi96Slk1amtPNHlJVE5oQWlzZWc9PSIsInZhbHVlIjoiV002WHFlaXRYelRIQ3J5OVZka3VwaEl3S1pnOGFRQkFNOUNqQjdGNmRVdWV2QlZUenZzVFBNTWVyR0FkNEZxd1pBemVZcGMrSGhaOGFPbzFaMFFzNVNlVjhBYkZPZzlZUWw5VTJwQll1Um1ZdXNXS05Sb1pabVNsTlQ1aVBBeERLTTFlQnkrbjByZUpCb3J6RTR4K3dMMS9QNXMxNXlBTU5XMDZmZVFaWk9NZWVGQVU3cEJOVlhyWHVSTzUxRFlHTjlNUFZiVVQ4QzdER0hna2cybUFKVEtIR3JJWHJ3eXBEWmxBenh2TkRQME9xQXpvSUxtTStoeFBiZ0FreXpGZ3VVeURTb3JlSWhQcFRTV05WaWpmK1cvd05lU1U3aWpmQjQwT2tqZkUyb0EwNk1ZVm9OZzVsdFYvYk5NbHBDOWdXYUFZTlVQMllpbUhlNk1LS3p2NGNyT2RFTVRPUHVtWE1ORDJQZCt6SHQ5dTFxK25KK1ZJdUlyUkFRTE56QkIzY0lzTzBudEF2VG5zekhYZmhHMFR5NGwxWjhRMURYcXcwZnV6ZVJrVElpNE5Sc2tMTnlGbWdYTHFkUTJpTk1DM3FiMVRYcEwyK3VGQkI0WUtGUHVabEZPUkhRd1ZTUkhaUFEvY3h4STZxaDNqc0paQmVBYmEzOGZjOTc4QXRyZ1QiLCJtYWMiOiJjM2E0ZDE3ZDcwMmE3MjkyYTQ4OTBjNTAwMGRlYTQ1MGM2OGE0NGJkMjVkMjdkZjE0ZDY1YzA1YWFiNmMzZmJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFFeGZqUkphdURscFF0OG8vVGJHRGc9PSIsInZhbHVlIjoiRnJzZU4rKytud09iSzB6SjF5dGJBYk9kcmdtRFdFdWI2K2JEbmVkM000YWEzL1crTHBjV25uNk85bTZGVjI2RmhCTE9kVFg0eVU0d0hIbFJkVlNRbmU4QitUOFVMRThoTHFRVmZTSndHSVZRZ29ZdWZleVNpM1pHSTRhVVFIVDJNN0tZWVVESUJ4Mlo4akhlekhTVEdLaVRQTDJOYnhQMC84UHZld2I0QWtQZnRqZEVOSThJYzhDWmluUmc2UVRpVTlpenc2VDB5VktoVGVWVnc0ZDlibmoycXJXQUdBS0haNmpNSUZ0dWFydUVIV2lLZ2xMdG9OWUJNOThBMXlVdWlrcnJ0K01lSDBzcW5SR1dMMTlRbVl1SlcrcHNCRitYWWJPNTY4ZUE1RnlkR3B3UWE5TlRyelRxOGNSNEZURTRuM05vbXNQL1ZzYXNtWE9hS0JKS0tVSFNNTzNQSmM5VXN5VVNPQkN0dHdwN0ZZVUJHRStnQWtpUTREQWo0OGJ4NjBRRVpRSGg4OG11bC9MR1hkUGR3cWhzM2VSMEtPQzREVTJ4ellJcG9IRDJDZFB5NVJ1aUowTFFyWnkwbkZiWlZ5cnV6dFJpSUZHT0lCZ3lOMUR2bXI3RzFGQzVCNHl4ckdyQjVtQUNXTytsVEZjeEhCQUhUWnZ0enNIU3BWLy8iLCJtYWMiOiJiOGI1ZTQxZWI4YjJlMGMyOTdlZWVlMmFjODNjZTE4NzM5YzQ1NTg3MDBlZTFkMDI2NjIyNDE2NDIyZDJlNDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660420110\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1459692311 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1459692311\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1956774986 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:18:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Iis0SVcydnlNNFQ5ZTFpVjl0a1V4ZkE9PSIsInZhbHVlIjoiWDZIMGRBMTRCa3dtSlRRbFZPR2RDS1Bmd2p2bktZZ1FmQllwcXRUOWJwTzhSc3pkZTErenJKL2RjTHkwd3pXVWpGMmxDRjExQ3FPb3E4T0VTK1hBWCtOMGEvSXN0ckNpY1RuYUpUS0cwdzJMMUVJRUkwek4wcVdUdkxNcHA4OGdYK1R6a0RkTVhURzhob1NtSmlCS3NzY0tZQ3kreW5ZL3JqaUx2RnBBV0tIYXBjNnFqMWkwMmlqSkM1Qkwya254a3lrWDBKUnJNZlN0a2F2TmU4dDZiM21NdDlOaGlGTUVaUXZSU0puK1BkS1h5dndYQWt1UmVISmMrbXNxL2hsLzJWOG84SWc1WDhOTzdKclZyYkRkbXlNUFVPbzMyL2N0TWRpSEZQM3lDQkNOZHZDZ2k2NjRKOTBzK2JHUFZXRVFmbytZMWoxbzI1ZlZNa2sraVZKTGU5d2xENnpFb0Z2cjY1NU9MWnJQQy94ekZJQVQ1WkVXbjBhbzZ3d0YxSSsvYXQ4b3hsSEtRbGIxZVd2ZVRnWUJUc1hNK3ExdTh0bUYwdG9YOHFlSWZUOUwzTm1uTUJkU2VwT1l2TDBXeCtQb0EzK1NWZkpUZWpkNzJsem9rSE45WWFwc2JhNEFvQitJVjFkWDN1RmlJRm9VdmNaSHcxTHpoQ1UvNUZ4clFYN3EiLCJtYWMiOiJmZTg2ZjQzMjJjZDI2Njg0MTg3MmM5OTE5NjhmZWFhMWRkMmJhYmM4ZWFjYTYxNWRlYzJmYjcwODM3ODBiNDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IkNFVkN0alVwOStDT1dnWWhzNmFnaXc9PSIsInZhbHVlIjoibiswK0NpTC85WmVPVGNNa0JYZzJWbTcvbm4vN2VuUXJXQ1p2aTRZTGJaU1piUzZVRVRiRDhOY2h4bUY2cEs1U2tOdStPM1JzWVBsc21YeTFFa0dJbnB4WEZIZ0JRVGFPUVRDbFFlMWhLWDQ5akptbjJRVUNPODV4SURNL3V1OVNuRUhGYkUxWHFXaFdWSHRVRFd3WVppM1NvVVpyZzJIV3M0bHh0Y29mNWxrQXFETjFhc01kVEpPZE1QSXFWK3R3RmtJKy9Xd2R0cDl4R3RmM09NWnl0SlBTODVVR1JEQmdWZXVTYWpWTGRtdTc0cTliOHZEQXcrT2wvOSszbjB3ZUJHT1BsSEcrVEFvdG5hVFp2NDZvQ3ZiL3ZmaHFaMUo2bEdYRzhFQU5UajhnYnlaQ2tBV2VwRlUyaVUwUW1SZzJLdlZ6MnhodkFNREFEQVVoRDdmRXNGdmg5Z3RDZndhSmt0QUZnY0hlckJxaFNPSHRTMU1lUnI5TzdKSlNJRnkvZWdVUk1sSk5lT1RvNHlEaEpNVGZ3TDJETjh3QkcyNlFRTDJucHVTOTg1YUZOMXJqVVZqRjVlTG9PeVJUaDd0TWtDamxiNWFkZFVpTnRhbGdnVGRxcUxTa0tIcXc5czVhRUNPSmgzdFdaQVNZelArWHY3L1JDNklNTitBdjdQa2MiLCJtYWMiOiJhODk0ZTZmZTJkYjY3OTdlNGY0YWM0ZTIxMTE4YmZlMjkzYjE4YTE0OGNlMzVjMTU1NTcwOGRiZmI0ZjNlMDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Iis0SVcydnlNNFQ5ZTFpVjl0a1V4ZkE9PSIsInZhbHVlIjoiWDZIMGRBMTRCa3dtSlRRbFZPR2RDS1Bmd2p2bktZZ1FmQllwcXRUOWJwTzhSc3pkZTErenJKL2RjTHkwd3pXVWpGMmxDRjExQ3FPb3E4T0VTK1hBWCtOMGEvSXN0ckNpY1RuYUpUS0cwdzJMMUVJRUkwek4wcVdUdkxNcHA4OGdYK1R6a0RkTVhURzhob1NtSmlCS3NzY0tZQ3kreW5ZL3JqaUx2RnBBV0tIYXBjNnFqMWkwMmlqSkM1Qkwya254a3lrWDBKUnJNZlN0a2F2TmU4dDZiM21NdDlOaGlGTUVaUXZSU0puK1BkS1h5dndYQWt1UmVISmMrbXNxL2hsLzJWOG84SWc1WDhOTzdKclZyYkRkbXlNUFVPbzMyL2N0TWRpSEZQM3lDQkNOZHZDZ2k2NjRKOTBzK2JHUFZXRVFmbytZMWoxbzI1ZlZNa2sraVZKTGU5d2xENnpFb0Z2cjY1NU9MWnJQQy94ekZJQVQ1WkVXbjBhbzZ3d0YxSSsvYXQ4b3hsSEtRbGIxZVd2ZVRnWUJUc1hNK3ExdTh0bUYwdG9YOHFlSWZUOUwzTm1uTUJkU2VwT1l2TDBXeCtQb0EzK1NWZkpUZWpkNzJsem9rSE45WWFwc2JhNEFvQitJVjFkWDN1RmlJRm9VdmNaSHcxTHpoQ1UvNUZ4clFYN3EiLCJtYWMiOiJmZTg2ZjQzMjJjZDI2Njg0MTg3MmM5OTE5NjhmZWFhMWRkMmJhYmM4ZWFjYTYxNWRlYzJmYjcwODM3ODBiNDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IkNFVkN0alVwOStDT1dnWWhzNmFnaXc9PSIsInZhbHVlIjoibiswK0NpTC85WmVPVGNNa0JYZzJWbTcvbm4vN2VuUXJXQ1p2aTRZTGJaU1piUzZVRVRiRDhOY2h4bUY2cEs1U2tOdStPM1JzWVBsc21YeTFFa0dJbnB4WEZIZ0JRVGFPUVRDbFFlMWhLWDQ5akptbjJRVUNPODV4SURNL3V1OVNuRUhGYkUxWHFXaFdWSHRVRFd3WVppM1NvVVpyZzJIV3M0bHh0Y29mNWxrQXFETjFhc01kVEpPZE1QSXFWK3R3RmtJKy9Xd2R0cDl4R3RmM09NWnl0SlBTODVVR1JEQmdWZXVTYWpWTGRtdTc0cTliOHZEQXcrT2wvOSszbjB3ZUJHT1BsSEcrVEFvdG5hVFp2NDZvQ3ZiL3ZmaHFaMUo2bEdYRzhFQU5UajhnYnlaQ2tBV2VwRlUyaVUwUW1SZzJLdlZ6MnhodkFNREFEQVVoRDdmRXNGdmg5Z3RDZndhSmt0QUZnY0hlckJxaFNPSHRTMU1lUnI5TzdKSlNJRnkvZWdVUk1sSk5lT1RvNHlEaEpNVGZ3TDJETjh3QkcyNlFRTDJucHVTOTg1YUZOMXJqVVZqRjVlTG9PeVJUaDd0TWtDamxiNWFkZFVpTnRhbGdnVGRxcUxTa0tIcXc5czVhRUNPSmgzdFdaQVNZelArWHY3L1JDNklNTitBdjdQa2MiLCJtYWMiOiJhODk0ZTZmZTJkYjY3OTdlNGY0YWM0ZTIxMTE4YmZlMjkzYjE4YTE0OGNlMzVjMTU1NTcwOGRiZmI0ZjNlMDU2IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956774986\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-766079761 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-766079761\", {\"maxDepth\":0})</script>\n"}}