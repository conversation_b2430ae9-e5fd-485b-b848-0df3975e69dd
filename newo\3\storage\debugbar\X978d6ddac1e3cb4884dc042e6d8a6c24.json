{"__meta": {"id": "X978d6ddac1e3cb4884dc042e6d8a6c24", "datetime": "2025-06-17 12:18:43", "utime": **********.572323, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162722.884791, "end": **********.572347, "duration": 0.6875560283660889, "duration_str": "688ms", "measures": [{"label": "Booting", "start": 1750162722.884791, "relative_start": 0, "end": **********.464513, "relative_end": **********.464513, "duration": 0.5797221660614014, "duration_str": "580ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.464536, "relative_start": 0.5797450542449951, "end": **********.572349, "relative_end": 2.1457672119140625e-06, "duration": 0.10781311988830566, "duration_str": "108ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01656, "accumulated_duration_str": "16.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5159478, "duration": 0.01481, "duration_str": "14.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 89.432}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5470068, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 89.432, "width_percent": 4.65}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.557394, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 94.082, "width_percent": 5.918}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1530346183 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1530346183\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1258230074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1258230074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-558936404 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558936404\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1052450031 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRsRi96Slk1amtPNHlJVE5oQWlzZWc9PSIsInZhbHVlIjoiV002WHFlaXRYelRIQ3J5OVZka3VwaEl3S1pnOGFRQkFNOUNqQjdGNmRVdWV2QlZUenZzVFBNTWVyR0FkNEZxd1pBemVZcGMrSGhaOGFPbzFaMFFzNVNlVjhBYkZPZzlZUWw5VTJwQll1Um1ZdXNXS05Sb1pabVNsTlQ1aVBBeERLTTFlQnkrbjByZUpCb3J6RTR4K3dMMS9QNXMxNXlBTU5XMDZmZVFaWk9NZWVGQVU3cEJOVlhyWHVSTzUxRFlHTjlNUFZiVVQ4QzdER0hna2cybUFKVEtIR3JJWHJ3eXBEWmxBenh2TkRQME9xQXpvSUxtTStoeFBiZ0FreXpGZ3VVeURTb3JlSWhQcFRTV05WaWpmK1cvd05lU1U3aWpmQjQwT2tqZkUyb0EwNk1ZVm9OZzVsdFYvYk5NbHBDOWdXYUFZTlVQMllpbUhlNk1LS3p2NGNyT2RFTVRPUHVtWE1ORDJQZCt6SHQ5dTFxK25KK1ZJdUlyUkFRTE56QkIzY0lzTzBudEF2VG5zekhYZmhHMFR5NGwxWjhRMURYcXcwZnV6ZVJrVElpNE5Sc2tMTnlGbWdYTHFkUTJpTk1DM3FiMVRYcEwyK3VGQkI0WUtGUHVabEZPUkhRd1ZTUkhaUFEvY3h4STZxaDNqc0paQmVBYmEzOGZjOTc4QXRyZ1QiLCJtYWMiOiJjM2E0ZDE3ZDcwMmE3MjkyYTQ4OTBjNTAwMGRlYTQ1MGM2OGE0NGJkMjVkMjdkZjE0ZDY1YzA1YWFiNmMzZmJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFFeGZqUkphdURscFF0OG8vVGJHRGc9PSIsInZhbHVlIjoiRnJzZU4rKytud09iSzB6SjF5dGJBYk9kcmdtRFdFdWI2K2JEbmVkM000YWEzL1crTHBjV25uNk85bTZGVjI2RmhCTE9kVFg0eVU0d0hIbFJkVlNRbmU4QitUOFVMRThoTHFRVmZTSndHSVZRZ29ZdWZleVNpM1pHSTRhVVFIVDJNN0tZWVVESUJ4Mlo4akhlekhTVEdLaVRQTDJOYnhQMC84UHZld2I0QWtQZnRqZEVOSThJYzhDWmluUmc2UVRpVTlpenc2VDB5VktoVGVWVnc0ZDlibmoycXJXQUdBS0haNmpNSUZ0dWFydUVIV2lLZ2xMdG9OWUJNOThBMXlVdWlrcnJ0K01lSDBzcW5SR1dMMTlRbVl1SlcrcHNCRitYWWJPNTY4ZUE1RnlkR3B3UWE5TlRyelRxOGNSNEZURTRuM05vbXNQL1ZzYXNtWE9hS0JKS0tVSFNNTzNQSmM5VXN5VVNPQkN0dHdwN0ZZVUJHRStnQWtpUTREQWo0OGJ4NjBRRVpRSGg4OG11bC9MR1hkUGR3cWhzM2VSMEtPQzREVTJ4ellJcG9IRDJDZFB5NVJ1aUowTFFyWnkwbkZiWlZ5cnV6dFJpSUZHT0lCZ3lOMUR2bXI3RzFGQzVCNHl4ckdyQjVtQUNXTytsVEZjeEhCQUhUWnZ0enNIU3BWLy8iLCJtYWMiOiJiOGI1ZTQxZWI4YjJlMGMyOTdlZWVlMmFjODNjZTE4NzM5YzQ1NTg3MDBlZTFkMDI2NjIyNDE2NDIyZDJlNDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1052450031\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-507032849 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507032849\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-399806012 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:18:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZDTnVCZUFpNXJad2hTbWEzcDNmK1E9PSIsInZhbHVlIjoiT016VnozNnA1UzV3dWU1WlVyZDMwaVRXNFY3L1VuemwrMTJ4WGloc25mV1c5VjBRL1NFYUg1RjFQcjJRRUUxZGdsaXBidG9Wa1RDc0phT0twalNFa2YyOWZRNmtpTzNWbHVTckdxYmdZd3BFTTc4NUNSa3lXK3hQZ3ZqenFCRmJmb1ZQNit2cjN2QU40UjY3ZmZMSjJ4YUwvcUdMS0FGQloveDNVU0VERHdWWnVpK0NmNEVJaWxBalFmRUlqOTd5SFo0a2M0WGJiYXQ3N203Z3BZL04vMnpXclc2VEI1TFBhekFVZzRaMTA0UzBjVjBWcnpRV1dBT3VBd1lyY00zYXViZUlMV29Sb1ZEb0oxVkRSVFRNMzByTUNLV0gwYms4ZjkvcjRRelFxS3hlUThBR0RsRVRTN2UxZGx6bTdKYmdYWEhFWU0rTmlibE9vd1NmUTRHeHJuOGdrWjQySFRuUWJzeWNVeSs1aEY4SXh2OVJ0dFlWSmUrNCsxb3pkQzdGcXZnR0RMUUtYQjVYZm1VMitwM0xrc1p0Yll6OFRaRUxvWjBZTzEyZmJPbndXT1ZwN1hCTkdsekRoNlFDbHVUdTNUek1uRStCK2thb1c5T1hBeGE4d0FWOFpiaTZrVnJHeVowYWFENW1FWG1WWVdyL3FmbTAwcmRHSDFMMDVrbWYiLCJtYWMiOiJmZjg5ZGE5YWM0MjM1NDg0ZTE5YWZmYTI1NWE5ZGQ3YzE4OGMyOTY5YTFmN2E1MTA0ZGUwNWExYTJhMTVmZTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imp4TVNwaUZabUZKdEgwMitTV09vbWc9PSIsInZhbHVlIjoiK1I5ZkppMVJuTWcweVFwWXdCbWc1RHdoRjlRWnFWaXVLUEFwclhaanduOUs1VG1JK3A5QUxNU2E1M25tRkJPdys5alFja2E2L1hDNUZxdndXMmdzVlUwN3ZKMTVJeVJydDhyWTZJNmY0N3pNN2hiZzdIZHp0dXMvelkxVGdrSTFVZGk0YlBDdXM5M25uUExZNVNIQkNWdmFZUUZmOEZzWlZITHNoeVpMY3Q0YnBieUV1Mmt0WmRMUGtDdXBRRlp3UVZ2V3N6dit6a0ZqdkJQVGlTTUlUTFJkRGZnYWJEZXBmamIwNUVPV1dUTFRFNW1kb3FNcnBucVJnK3RsUlhqTXlDd21henpLSmRCNzNoaW5JOW5GaVVuU1RzcWtmNVhLY3FYK1E3emhCRnpoamp4TkQxTmY0RndvRXp3YmxvaWJZdjBBdkFlM3lLZWV0QWhKUGt5akhUZ0lkbTV1Tkd5VTFzclFMNFhDdHJSNlZTVTlZY3VKaUxHRmU3WU82Z09ySnQ1cFV2QWZ3Q0poZlhVbjB0QjVsakhhZGd5WG0wV3BLWkxudmRSUllkVTJraUd2dnZnRWFnbFhGNVdQZlA5WURvaVhGMDUrOWk1UlcvMWlQM2pCS2xtN1VBWlVBRUc2dDcveld6bE9pYTZhV0RnS1M4dHdGSkp1UzZ2Q0xJWHAiLCJtYWMiOiJiNTA3NTY5NTZmYWMzNTQyMzViY2JiMzkxY2I4M2QyNDgxNDNhNGIwMmQzY2Y3YjNlNGI4YmRlMjAwOTlmMGY4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZDTnVCZUFpNXJad2hTbWEzcDNmK1E9PSIsInZhbHVlIjoiT016VnozNnA1UzV3dWU1WlVyZDMwaVRXNFY3L1VuemwrMTJ4WGloc25mV1c5VjBRL1NFYUg1RjFQcjJRRUUxZGdsaXBidG9Wa1RDc0phT0twalNFa2YyOWZRNmtpTzNWbHVTckdxYmdZd3BFTTc4NUNSa3lXK3hQZ3ZqenFCRmJmb1ZQNit2cjN2QU40UjY3ZmZMSjJ4YUwvcUdMS0FGQloveDNVU0VERHdWWnVpK0NmNEVJaWxBalFmRUlqOTd5SFo0a2M0WGJiYXQ3N203Z3BZL04vMnpXclc2VEI1TFBhekFVZzRaMTA0UzBjVjBWcnpRV1dBT3VBd1lyY00zYXViZUlMV29Sb1ZEb0oxVkRSVFRNMzByTUNLV0gwYms4ZjkvcjRRelFxS3hlUThBR0RsRVRTN2UxZGx6bTdKYmdYWEhFWU0rTmlibE9vd1NmUTRHeHJuOGdrWjQySFRuUWJzeWNVeSs1aEY4SXh2OVJ0dFlWSmUrNCsxb3pkQzdGcXZnR0RMUUtYQjVYZm1VMitwM0xrc1p0Yll6OFRaRUxvWjBZTzEyZmJPbndXT1ZwN1hCTkdsekRoNlFDbHVUdTNUek1uRStCK2thb1c5T1hBeGE4d0FWOFpiaTZrVnJHeVowYWFENW1FWG1WWVdyL3FmbTAwcmRHSDFMMDVrbWYiLCJtYWMiOiJmZjg5ZGE5YWM0MjM1NDg0ZTE5YWZmYTI1NWE5ZGQ3YzE4OGMyOTY5YTFmN2E1MTA0ZGUwNWExYTJhMTVmZTg5IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imp4TVNwaUZabUZKdEgwMitTV09vbWc9PSIsInZhbHVlIjoiK1I5ZkppMVJuTWcweVFwWXdCbWc1RHdoRjlRWnFWaXVLUEFwclhaanduOUs1VG1JK3A5QUxNU2E1M25tRkJPdys5alFja2E2L1hDNUZxdndXMmdzVlUwN3ZKMTVJeVJydDhyWTZJNmY0N3pNN2hiZzdIZHp0dXMvelkxVGdrSTFVZGk0YlBDdXM5M25uUExZNVNIQkNWdmFZUUZmOEZzWlZITHNoeVpMY3Q0YnBieUV1Mmt0WmRMUGtDdXBRRlp3UVZ2V3N6dit6a0ZqdkJQVGlTTUlUTFJkRGZnYWJEZXBmamIwNUVPV1dUTFRFNW1kb3FNcnBucVJnK3RsUlhqTXlDd21henpLSmRCNzNoaW5JOW5GaVVuU1RzcWtmNVhLY3FYK1E3emhCRnpoamp4TkQxTmY0RndvRXp3YmxvaWJZdjBBdkFlM3lLZWV0QWhKUGt5akhUZ0lkbTV1Tkd5VTFzclFMNFhDdHJSNlZTVTlZY3VKaUxHRmU3WU82Z09ySnQ1cFV2QWZ3Q0poZlhVbjB0QjVsakhhZGd5WG0wV3BLWkxudmRSUllkVTJraUd2dnZnRWFnbFhGNVdQZlA5WURvaVhGMDUrOWk1UlcvMWlQM2pCS2xtN1VBWlVBRUc2dDcveld6bE9pYTZhV0RnS1M4dHdGSkp1UzZ2Q0xJWHAiLCJtYWMiOiJiNTA3NTY5NTZmYWMzNTQyMzViY2JiMzkxY2I4M2QyNDgxNDNhNGIwMmQzY2Y3YjNlNGI4YmRlMjAwOTlmMGY4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-399806012\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-702247841 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702247841\", {\"maxDepth\":0})</script>\n"}}