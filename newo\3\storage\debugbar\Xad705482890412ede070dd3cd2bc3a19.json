{"__meta": {"id": "Xad705482890412ede070dd3cd2bc3a19", "datetime": "2025-06-17 12:13:20", "utime": **********.208898, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162399.498848, "end": **********.208918, "duration": 0.7100701332092285, "duration_str": "710ms", "measures": [{"label": "Booting", "start": 1750162399.498848, "relative_start": 0, "end": **********.125392, "relative_end": **********.125392, "duration": 0.6265439987182617, "duration_str": "627ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.125414, "relative_start": 0.6265659332275391, "end": **********.20892, "relative_end": 1.9073486328125e-06, "duration": 0.08350610733032227, "duration_str": "83.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45405232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1688\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1688-1698</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00605, "accumulated_duration_str": "6.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.176286, "duration": 0.00507, "duration_str": "5.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 83.802}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1970012, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 83.802, "width_percent": 16.198}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-819996487 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-819996487\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1230207014 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1230207014\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1612105672 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612105672\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1196290152 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZ6REpBdVJPazY4Y1FFcCsvampsRkE9PSIsInZhbHVlIjoiSWJNSHhQbVAwTExZT0txWW1iK1JGY2tIckpPME50eURvKzluR2hHeW5nOW1TU3J6R05HTVdDdEMxNmRhYnMxeHR2RVZzRlRWQnlkSDRRcEhVR3hDUHF1aWhveGU5bTUweERDRVEvVHA1cjBHU292TUdmbitjMmcrczNPNWVHVUFmTno0OVF6bW4xb2hOQllwSmlpSitFTmdkTXlkY1pXS0NUOHBQOE1sUlpGcmJLS05ENVZYNW9yUWxaZWRUUGlTZ3JQb09iZjU0ck1JNTFmb2IzTjE5RFFoY0tnSG1Gb2xQclhVOW1xUkJ6MlQzSy9icWpCVGY1bjlWMFJaYWx2QmM1NHltUkwwRFZCVEJhVW01ejlCM1hWNm9aZXJOTDRVVDc4cEpSajFxR2xiNDVPUUVYenZFWDBXYTczNmZkbGJRRHdOcFE1ZnNSMFp2eGhIcVdGNHVGMmZOZkYzYWZUSG9MdDZoRUQxQmgyY25rVUFOTUFnQkZmV0NCN0pNYjVHb1VNbHJMdEdkc0tYWVdUYVRwM2g2Z0ZJVGVSdjlUdTNQQVFUWGViWkRzb1JnN0x5R1BwenBYczZheXd6NGVqZFVPRVZHVnl6NkdDM3BEZ24yQS9sZUZiZXd0dEVMdVgrN3NQZDBLdHJKeldFeDN3a2VrOFE2U2lJZXFER1BhMTgiLCJtYWMiOiJkZjg2ODVmZTQyMjUzMGRjYWUyY2E4Mzc3MTE0NmJlODdjYmZiY2YxNDU3NWFlODNjOGI0NjNkZjJlNjNkZDVjIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjBNTVE4TnVnOENuZmdnNWJscWliS3c9PSIsInZhbHVlIjoiVkU3MkdOejc1OTlOMHRjT0RET1h2YzdSTTRlbkF6bVZPSEtneXJCdDdFcDRhbVR1UjFFc3ZzZHFKWXdjUWgyWWh1d2VUN1Z1Y2g3UUFLMzEzamo2TG9MK3I4SXlLMUQyVXR6MklTdEZSaW8zVWcvRjVhT1FPNjhUQ05VaUZiR3Y3YnlXb1duQ2ExZUpnOFBqUlhLeWg5WTlCVXlBSnhoNTF4RjJwWFFLc3NXcG5qcEhYVHd6UGJDdyszQXU5ajljNi9EZXdzaWZWdEFWMGN6OWQ2MFFLSnVEaUVLTTZPU2JRQmVFTm9VWnV4Vy8xeDZuQkFleHI0UmlRWHhPMCsrQ25oSzlkazNoTGpFeTY4VXJEOGVYa3B4Qi85M2JORktPWmtMQ0t3RFBzbC8zYXRzQmpLaU5lYWxKT3BvZTJwSVdoeHhNNVk5WHNraHRPUXNJSXVmS28weFNpMWsxdlAvVE9TNkpYVFRRZTl4aGk4OXR0YkZmbnVORlh2cG91ZzdkOUdPeW9YWGUycXBUY2JnZHQxbysweU9BbTJWTGs3dythTGoxWmhYR0IzckF6clhBQ0ZVRytCbHNhdWNCQis3S1kyeHpmcCtjSHlZQW1YVlBMTUxHWmlFK2N4bi8xZlczZE9hWDBFWEk2clNVQ29pUEZHR2lmRlBtWXRJNm12ZFUiLCJtYWMiOiIxNjEwOTdmNzM4N2Y1OGNiYmQ1M2U4Mzc3OWMyNDRkMzVlZDllYTRhYjk0NmFlM2RhMTc2NjhmMTRiOTMxMTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1196290152\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-338054218 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338054218\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1093280897 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:13:20 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imp2VFY2VUpUYWo0bktIYUhaMWRqYXc9PSIsInZhbHVlIjoiZUllRFZweDg0TjdOckxSWlRjeTV3L05GRXFmZEFRaWkyZHp6cXZxVVFXbEw3ZEl6MGtrWFdvUnNjVE45MXlsRjFWZXk1c0JLbkM5aFFMOHhLUUhmTzNrRUdVYXlDZnBqazN6dVVST2xueU90MExKZEd0dXNBS1RFaXMzdnBGU2ZhY0Vxd1pCN1UvS2lpRGRDVUd6T3dMaFU5Z2RpTTZsYjE2eGxtMGo0MVB4TElsNU1QdTVyMGJrTk5rVUpBUEhRQ0c2YVYxcEE3c1M0SXBxZVlXVEJqRUh3WkNwSEJBaVBaVVFnRnhDSHdlZWp1cGZKWHU1aEVZQjB0MEw5c1NPbUVmaU04dGV0K2NnUTFmczdYM1pEakxjaVdRbHVTalY5a0prK1p5NTc5YXRqTlRiSnFhOWZ3WFhhOExxSUNoNmpla0gzOEkwT0JsWEw5OWZzS3dSZEJGSWd0bHhFOEo0TThQQ2oyM0RiSTJmUlg3S1E0OCtiSTZKa044QWxRZzM1c1RXSXFYcHRJN09qd0pPL0RSaTB3bWhPeGdiOTg1ZUFRVG5XRGhHSGF0YzFZSVFOaUZhU2ZwTnlZcDRVQ0MwcVdOUHZKU0ZDTUhxS1ZqVE15SEdTenNYWFBBZ1RodGEvNGVGZTFSeHRpUklaVWpUbHlQcWp0MVRQSGtNdlpWVU8iLCJtYWMiOiJiYzI3MTJiOWJjYjFmOWUyODk1NDU5YTg2OTYyZjIxM2I0NjNkNmI1ODVkZTAwMmEyZjhiZmUyYzgxNDViZmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:13:20 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Im9iT3FLQlZZc3IyZmF2emROSHpRaWc9PSIsInZhbHVlIjoiWENnb0RVaXNuUnljTDFab2NNd1JjMUxJMWFGMTNPcEdwZEpuZWd3NnQxOW5TY2NIbUV6bklyWWFjU1F6MGlvaHZ4UWU1K0M0RVFySitPcXB4K2c4YTYyd202RUtoYW9Db1NqcUtnWnA1NHQ3bk5HNmZNbVd5dUZQMUpLZmZOSzQrTWRtakVQS0xEd2hHRGNaV3dSRlUrUU1Vdno1YmJWWjgzeUQvUHpMdEdyK2p3UWlBSS9aNjU1WXlyYzlxVEYzQUVVeGxJOEZFN2R3UFBjd2kyUmZ5cnk4NWMzRml1ZU9DRUlzRVkraWt5ZEtpVERkc3QzMmlIMnhRRTN6d0lxYTVMQ1hySDBSZ1h0OFhnQzdHRGNtY2YzRDE2ckM1SE92R1FDc0xDaHdJRFkzUk56QWNkenpwVmtETGExZy9NOXVITHBvM2NzdmQzQ09ZWEpOanY0czhEV3dnckZlVS9yMzlkMFd1K3dsbFREMmxDeXJLSzVQVHBaYWRUcjVrWkZyRE1KRy9lTDJmdDl5cGJSMytDZDlVT3NSU1dzUldmTnd4SHNIVmpJWVpWQmpweFVodXhMaEQwQnFISi9ha1Q5T0IzZ1RMKzMrWWNULzNwbU9ZQXRINTJERzZoUUo0VjhacHVyaVUyMTgwclJBYTdKSXc1UkFBcGh3TnFrRWdVS1YiLCJtYWMiOiI2NWI2OGJlOGVhM2FmNjgyZTVhNmM5YWMwNmU2Y2Q5MmQxNTZlOGE1ZDU5MmU0NjI4ZWNlOTJiOTA2OWNhN2IxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:13:20 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imp2VFY2VUpUYWo0bktIYUhaMWRqYXc9PSIsInZhbHVlIjoiZUllRFZweDg0TjdOckxSWlRjeTV3L05GRXFmZEFRaWkyZHp6cXZxVVFXbEw3ZEl6MGtrWFdvUnNjVE45MXlsRjFWZXk1c0JLbkM5aFFMOHhLUUhmTzNrRUdVYXlDZnBqazN6dVVST2xueU90MExKZEd0dXNBS1RFaXMzdnBGU2ZhY0Vxd1pCN1UvS2lpRGRDVUd6T3dMaFU5Z2RpTTZsYjE2eGxtMGo0MVB4TElsNU1QdTVyMGJrTk5rVUpBUEhRQ0c2YVYxcEE3c1M0SXBxZVlXVEJqRUh3WkNwSEJBaVBaVVFnRnhDSHdlZWp1cGZKWHU1aEVZQjB0MEw5c1NPbUVmaU04dGV0K2NnUTFmczdYM1pEakxjaVdRbHVTalY5a0prK1p5NTc5YXRqTlRiSnFhOWZ3WFhhOExxSUNoNmpla0gzOEkwT0JsWEw5OWZzS3dSZEJGSWd0bHhFOEo0TThQQ2oyM0RiSTJmUlg3S1E0OCtiSTZKa044QWxRZzM1c1RXSXFYcHRJN09qd0pPL0RSaTB3bWhPeGdiOTg1ZUFRVG5XRGhHSGF0YzFZSVFOaUZhU2ZwTnlZcDRVQ0MwcVdOUHZKU0ZDTUhxS1ZqVE15SEdTenNYWFBBZ1RodGEvNGVGZTFSeHRpUklaVWpUbHlQcWp0MVRQSGtNdlpWVU8iLCJtYWMiOiJiYzI3MTJiOWJjYjFmOWUyODk1NDU5YTg2OTYyZjIxM2I0NjNkNmI1ODVkZTAwMmEyZjhiZmUyYzgxNDViZmU4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:13:20 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Im9iT3FLQlZZc3IyZmF2emROSHpRaWc9PSIsInZhbHVlIjoiWENnb0RVaXNuUnljTDFab2NNd1JjMUxJMWFGMTNPcEdwZEpuZWd3NnQxOW5TY2NIbUV6bklyWWFjU1F6MGlvaHZ4UWU1K0M0RVFySitPcXB4K2c4YTYyd202RUtoYW9Db1NqcUtnWnA1NHQ3bk5HNmZNbVd5dUZQMUpLZmZOSzQrTWRtakVQS0xEd2hHRGNaV3dSRlUrUU1Vdno1YmJWWjgzeUQvUHpMdEdyK2p3UWlBSS9aNjU1WXlyYzlxVEYzQUVVeGxJOEZFN2R3UFBjd2kyUmZ5cnk4NWMzRml1ZU9DRUlzRVkraWt5ZEtpVERkc3QzMmlIMnhRRTN6d0lxYTVMQ1hySDBSZ1h0OFhnQzdHRGNtY2YzRDE2ckM1SE92R1FDc0xDaHdJRFkzUk56QWNkenpwVmtETGExZy9NOXVITHBvM2NzdmQzQ09ZWEpOanY0czhEV3dnckZlVS9yMzlkMFd1K3dsbFREMmxDeXJLSzVQVHBaYWRUcjVrWkZyRE1KRy9lTDJmdDl5cGJSMytDZDlVT3NSU1dzUldmTnd4SHNIVmpJWVpWQmpweFVodXhMaEQwQnFISi9ha1Q5T0IzZ1RMKzMrWWNULzNwbU9ZQXRINTJERzZoUUo0VjhacHVyaVUyMTgwclJBYTdKSXc1UkFBcGh3TnFrRWdVS1YiLCJtYWMiOiI2NWI2OGJlOGVhM2FmNjgyZTVhNmM5YWMwNmU2Y2Q5MmQxNTZlOGE1ZDU5MmU0NjI4ZWNlOTJiOTA2OWNhN2IxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:13:20 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093280897\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1550125807 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550125807\", {\"maxDepth\":0})</script>\n"}}