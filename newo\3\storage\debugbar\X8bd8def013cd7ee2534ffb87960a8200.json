{"__meta": {"id": "X8bd8def013cd7ee2534ffb87960a8200", "datetime": "2025-06-17 12:19:32", "utime": **********.066774, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162771.466066, "end": **********.066796, "duration": 0.6007301807403564, "duration_str": "601ms", "measures": [{"label": "Booting", "start": 1750162771.466066, "relative_start": 0, "end": 1750162771.970776, "relative_end": 1750162771.970776, "duration": 0.5047101974487305, "duration_str": "505ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750162771.970794, "relative_start": 0.5047280788421631, "end": **********.066799, "relative_end": 2.86102294921875e-06, "duration": 0.09600496292114258, "duration_str": "96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45531776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006789999999999999, "accumulated_duration_str": "6.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.023577, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 67.599}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.046793, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 67.599, "width_percent": 18.115}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0540001, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 85.714, "width_percent": 14.286}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1137158878 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1137158878\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-393119443 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-393119443\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-920976615 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920976615\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-563508333 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162761627%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR1THZNL2hrdUpMSWpJRHovMnBqdVE9PSIsInZhbHVlIjoibXRMNC9WaFUxWUhGc1BxNEdocGZObmFRanhMUm9XU1RmQWhFMzNBYjBxZENhdkJ1WVE0dllhMExsUlRFdlpIR2RGOXpJR01CRkdEcDExaUNQaUNRYzAzOXhzOUN0cE0xYUZOa0xwb2FRenFwRVJ4MkZrcU1yWkRhbHByUkZ6SERFc3RmY3ZocjBWQVk5Rld3OEFTWTFhS2NNK2NPVHloZ0V4cHFKazZNYjN0US9TSmRDS1ZiWnFuOXp2ZnFBOUlsVmtnQ2ttQ0ZvRWpLc2pQaXFEQjgvSmMxRms0VFg4L3o4K1R6T09tZmw1SHhMV2JVSmhtMWdxSGhJU0NTQkVrTnJUc3dpZFlzRThJUWZTWE5rS3h4emJwN1YzUGtGYUJlM2d4SHgwMXVqa1MvMXlYcFZjSWx3TzJjOTU0ZTZwblFLekM2KzgxcnhDWXovaXZSRHl2VGNkelNzdE1HSElWaVMrNFdXZStjY2x5aUlQdkc5UEQzUjNUUSszRkQzM1ZpQ1ZrL3NOUWVHTWxQekEvOHZqbm5lV0xVWTJ1SWRQcGZhOHlJeW1Galc3SmtsQzJ3Y0RPQnRJOEtDNWtaajlZR1FXSVhsMUVBajNtSWQ4ME05MDFsdEpucmxNWHFFc3Y4elAxZ2dtUmR6TnloNjFYaEhOazNpUHNBT25PVTYybUgiLCJtYWMiOiIxN2ZkMjU4OTJlOGI2NDVmNDkzZjA0ZGMyOWJiYmU1NzI1ZTUwY2IzZWZhYjQyMTMwNmI1YzVjOWY1YTNlZjE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ2WEUwTVBQZVM1TEZoVEphRmtJMGc9PSIsInZhbHVlIjoiMVFoS2QzMHQwalEzLzJhMStjZVJpWUlaZVdrZEZBb0VHTkZsMHdxMWRCeTh2UitsTVR6NjVoRWFZbkxhaUdpRWtWaEk5UCtheEl3MU45RTFQK3RvQSs5RVhXdjhKQmxsWEFZOFhqWTRkYXNtKzlDaVVuTEtJbVRIY0V5dVBBQ3FGWWd0dnpGek5nSlhKNFRVUzhSeFVCTlNjSGtqekFrZ2tzQ0JRSzJlM2lFemFlQnR1UjU3Ymc0akFGTWl4dFB6QlpIeWNhaktlcTdKMVhqUTN4RmEwV3puRXJoRDJBRnR6bkpLeWdWZ2c0ZlpoYkVCSkt5N0FPVitSK2wxWi9NOXVpVnhkeXNBQkNFd2NlbG01WFVROUROSk9zSkVtS1VPYy96VmNEamN3TXlMcDJTU2swUjRaQ3dsUGdnbHF4V1lMQjhiN01pcndLVVBhL3N3VGU0RnAzRzNrckk5elJUc1pVWnQ2ODhXY1VSbGh1VURsbkdtSkZzSW9qZnNVRm0wbHd5bnZzZmdsSzMyM3RNUktuaXJqUWJQZTY2K1A5S044a3lWSWVEL04yK3lBcHpjRENteDdJdS8vek5aVDV2NWYvR3NpMll0alRnbUlLM2NyNlRXaDdzY2d3Z1hCWUVGWnY2a0FPRmFIZDNrVHUxSUY0WTYveE9oWlg5eHliaWMiLCJtYWMiOiJhMmY0MDUzZTY0Y2E4NjFmMDdlMmU5M2NlYmQ1N2JkNzE1NTg0YWJhNDQwZTUyMjhlNDE2ODI1YjM0OWY3YmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-563508333\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-768075928 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-768075928\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1335394423 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNtL2NGaFlYOHJiYjZUQlBKeFU2OHc9PSIsInZhbHVlIjoicnZZK1krTlJnQjdwN1FGV0RzSlN0VWJOdUxvbndJUFFVQ0huL2Z2MmkxdWJsUXE4NmhpcnluYU9LYzRoVEppZ1FCclY4UGtnRkJMWjg2ODNnQzkxMXNWZm16Q1I2RkZub1RWeTdSZThxWFBBWk9DellScklocVR4V3hCejhoR3pUbU1XT28xS0U3QVlLSVdMdDBKWVdmNVR0OXE5NVgxMVlZRkVlRStScXhKcEUrRjZXTmR5dWZvUnMzaW5FRUQ1Q1lBWXRwWTlyT2xpL0k3T3FWei9OT3g3UG1ZOVZGaXp5MW1FR0FQa1Bib2VWNnhZTEZqbXJRVFpMeW41aEFleGNJckRGemVkQ0VodkR0VVZvWWczSUFJczdlMDR2WnpHMStuZGErVGF4SVRiSWhCb2Y2REtJdldZcC9uMHpCNW1MSlFHeWkxVTY1SlVndDZGUjVWaW8vZWVoQVdMeVArRnVrcmFkK0t2T0UvbjV6Z1dCL1pYaHFSaVdwWHllUXhxNWd6UEhjeFlrNVlFSVd2eVlQOWZ5MmE5a1FMOVU3OTRXQisrd2gwczMyTnA0TE1HT000Y254SFdnTXJwMGl5STErUXBUVnpMY0cwd1V3VVg1QmZHRjE2TWRLK2Rxa1VUdDNrWThBUkx6VlJTSm5CNHRtRU9laVhnaWdtK2Q3TkUiLCJtYWMiOiI2ZGZmNjU2M2Q0MzYzZWQ2NjdhYjRlYjlkNzRiZjk4NDY3NjhhN2FjYzU5NzBiMDcwNDdkNTY1NjRiNzQ0ZDlhIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6Imtsc0NxRHRGbHh5Zjd0MDU1MEFLWUE9PSIsInZhbHVlIjoiQlVMQU1EUTAvVUdTc3BBd3did2c4MXRRdU9xSHUvTFIvenFnYTZUWVRyTTZLck83eUw1OHJGQktETmNicGhYVVZucGR1VEUyRXgraDBKMjc4KzNLL3RWMEdpalVZbXR5cTVqbEFHKzM5NVNEYTlWQXlSR0xtNFo0L1pBcVJYOFFYRm1HU1JFSWdwV1JPOXR3eEhSNGR3RTVtOVYzcEVxUVRGZVE2dlF1aUVpc2FUYmE4MTJMdmJwSDlIWjR2Q0pRN1FhREF2QUJEdGt3OUs0QjQ3aWp6SzdITm85STZPNmZTcmlHaEk4QnB6cW5PTzZMWVc1ajI1TlBMMFNlbmsrR3NVbXAwVkI2cGpNSm9SRUVTTE1lZlRBd0xHRnpMZ05yaVh2bi9kVVk2eG0zN3RVNStHTDl6YVdZclZuY25DVWc4ZjV1TWlJd0hXL2I0dmFLV2FtTXppdE5KTmtLdVhPUlE4OTh4bEN5RUIydzRrdTRUOFBTT1JIcVZ0aHcva3NQK0I4Q2EzZ1NCS2QzUnFDUnlZUmdPRFdkUGFNQjVYZnVLcytHOU5vd3g0d0JJUE5zU2E5bDdYNGsyUGhLUDMrM05WV1duYU1YdnVtdnJUTUJMOUgzcUNEbHlHd0VHZ1lmOFJMK3lGUE4wWDV5WkVmbUp1cWZjRFUwc2pJTG1RbHIiLCJtYWMiOiJkNGFiZjYzYmY0YjBhMmRhN2QwYzJlZDIzOTc2NDU5Zjg0YzhiZWFiYjZlYzE0M2U1MGVmZTllMTZiZGQ5MjMxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNtL2NGaFlYOHJiYjZUQlBKeFU2OHc9PSIsInZhbHVlIjoicnZZK1krTlJnQjdwN1FGV0RzSlN0VWJOdUxvbndJUFFVQ0huL2Z2MmkxdWJsUXE4NmhpcnluYU9LYzRoVEppZ1FCclY4UGtnRkJMWjg2ODNnQzkxMXNWZm16Q1I2RkZub1RWeTdSZThxWFBBWk9DellScklocVR4V3hCejhoR3pUbU1XT28xS0U3QVlLSVdMdDBKWVdmNVR0OXE5NVgxMVlZRkVlRStScXhKcEUrRjZXTmR5dWZvUnMzaW5FRUQ1Q1lBWXRwWTlyT2xpL0k3T3FWei9OT3g3UG1ZOVZGaXp5MW1FR0FQa1Bib2VWNnhZTEZqbXJRVFpMeW41aEFleGNJckRGemVkQ0VodkR0VVZvWWczSUFJczdlMDR2WnpHMStuZGErVGF4SVRiSWhCb2Y2REtJdldZcC9uMHpCNW1MSlFHeWkxVTY1SlVndDZGUjVWaW8vZWVoQVdMeVArRnVrcmFkK0t2T0UvbjV6Z1dCL1pYaHFSaVdwWHllUXhxNWd6UEhjeFlrNVlFSVd2eVlQOWZ5MmE5a1FMOVU3OTRXQisrd2gwczMyTnA0TE1HT000Y254SFdnTXJwMGl5STErUXBUVnpMY0cwd1V3VVg1QmZHRjE2TWRLK2Rxa1VUdDNrWThBUkx6VlJTSm5CNHRtRU9laVhnaWdtK2Q3TkUiLCJtYWMiOiI2ZGZmNjU2M2Q0MzYzZWQ2NjdhYjRlYjlkNzRiZjk4NDY3NjhhN2FjYzU5NzBiMDcwNDdkNTY1NjRiNzQ0ZDlhIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6Imtsc0NxRHRGbHh5Zjd0MDU1MEFLWUE9PSIsInZhbHVlIjoiQlVMQU1EUTAvVUdTc3BBd3did2c4MXRRdU9xSHUvTFIvenFnYTZUWVRyTTZLck83eUw1OHJGQktETmNicGhYVVZucGR1VEUyRXgraDBKMjc4KzNLL3RWMEdpalVZbXR5cTVqbEFHKzM5NVNEYTlWQXlSR0xtNFo0L1pBcVJYOFFYRm1HU1JFSWdwV1JPOXR3eEhSNGR3RTVtOVYzcEVxUVRGZVE2dlF1aUVpc2FUYmE4MTJMdmJwSDlIWjR2Q0pRN1FhREF2QUJEdGt3OUs0QjQ3aWp6SzdITm85STZPNmZTcmlHaEk4QnB6cW5PTzZMWVc1ajI1TlBMMFNlbmsrR3NVbXAwVkI2cGpNSm9SRUVTTE1lZlRBd0xHRnpMZ05yaVh2bi9kVVk2eG0zN3RVNStHTDl6YVdZclZuY25DVWc4ZjV1TWlJd0hXL2I0dmFLV2FtTXppdE5KTmtLdVhPUlE4OTh4bEN5RUIydzRrdTRUOFBTT1JIcVZ0aHcva3NQK0I4Q2EzZ1NCS2QzUnFDUnlZUmdPRFdkUGFNQjVYZnVLcytHOU5vd3g0d0JJUE5zU2E5bDdYNGsyUGhLUDMrM05WV1duYU1YdnVtdnJUTUJMOUgzcUNEbHlHd0VHZ1lmOFJMK3lGUE4wWDV5WkVmbUp1cWZjRFUwc2pJTG1RbHIiLCJtYWMiOiJkNGFiZjYzYmY0YjBhMmRhN2QwYzJlZDIzOTc2NDU5Zjg0YzhiZWFiYjZlYzE0M2U1MGVmZTllMTZiZGQ5MjMxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1335394423\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1540739304 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540739304\", {\"maxDepth\":0})</script>\n"}}