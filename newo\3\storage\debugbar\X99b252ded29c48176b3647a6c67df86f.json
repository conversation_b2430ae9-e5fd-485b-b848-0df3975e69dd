{"__meta": {"id": "X99b252ded29c48176b3647a6c67df86f", "datetime": "2025-06-17 12:19:32", "utime": **********.075754, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162771.472185, "end": **********.075806, "duration": 0.6036210060119629, "duration_str": "604ms", "measures": [{"label": "Booting", "start": 1750162771.472185, "relative_start": 0, "end": 1750162771.98194, "relative_end": 1750162771.98194, "duration": 0.5097551345825195, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750162771.981954, "relative_start": 0.5097692012786865, "end": **********.075812, "relative_end": 6.198883056640625e-06, "duration": 0.09385800361633301, "duration_str": "93.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.006570000000000001, "accumulated_duration_str": "6.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.023466, "duration": 0.004690000000000001, "duration_str": "4.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 71.385}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0469701, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 71.385, "width_percent": 15.068}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.06151, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 86.454, "width_percent": 13.546}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1008919463 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1008919463\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1452296975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1452296975\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1609851040 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1609851040\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-356636872 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162761627%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InR1THZNL2hrdUpMSWpJRHovMnBqdVE9PSIsInZhbHVlIjoibXRMNC9WaFUxWUhGc1BxNEdocGZObmFRanhMUm9XU1RmQWhFMzNBYjBxZENhdkJ1WVE0dllhMExsUlRFdlpIR2RGOXpJR01CRkdEcDExaUNQaUNRYzAzOXhzOUN0cE0xYUZOa0xwb2FRenFwRVJ4MkZrcU1yWkRhbHByUkZ6SERFc3RmY3ZocjBWQVk5Rld3OEFTWTFhS2NNK2NPVHloZ0V4cHFKazZNYjN0US9TSmRDS1ZiWnFuOXp2ZnFBOUlsVmtnQ2ttQ0ZvRWpLc2pQaXFEQjgvSmMxRms0VFg4L3o4K1R6T09tZmw1SHhMV2JVSmhtMWdxSGhJU0NTQkVrTnJUc3dpZFlzRThJUWZTWE5rS3h4emJwN1YzUGtGYUJlM2d4SHgwMXVqa1MvMXlYcFZjSWx3TzJjOTU0ZTZwblFLekM2KzgxcnhDWXovaXZSRHl2VGNkelNzdE1HSElWaVMrNFdXZStjY2x5aUlQdkc5UEQzUjNUUSszRkQzM1ZpQ1ZrL3NOUWVHTWxQekEvOHZqbm5lV0xVWTJ1SWRQcGZhOHlJeW1Galc3SmtsQzJ3Y0RPQnRJOEtDNWtaajlZR1FXSVhsMUVBajNtSWQ4ME05MDFsdEpucmxNWHFFc3Y4elAxZ2dtUmR6TnloNjFYaEhOazNpUHNBT25PVTYybUgiLCJtYWMiOiIxN2ZkMjU4OTJlOGI2NDVmNDkzZjA0ZGMyOWJiYmU1NzI1ZTUwY2IzZWZhYjQyMTMwNmI1YzVjOWY1YTNlZjE1IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InJ2WEUwTVBQZVM1TEZoVEphRmtJMGc9PSIsInZhbHVlIjoiMVFoS2QzMHQwalEzLzJhMStjZVJpWUlaZVdrZEZBb0VHTkZsMHdxMWRCeTh2UitsTVR6NjVoRWFZbkxhaUdpRWtWaEk5UCtheEl3MU45RTFQK3RvQSs5RVhXdjhKQmxsWEFZOFhqWTRkYXNtKzlDaVVuTEtJbVRIY0V5dVBBQ3FGWWd0dnpGek5nSlhKNFRVUzhSeFVCTlNjSGtqekFrZ2tzQ0JRSzJlM2lFemFlQnR1UjU3Ymc0akFGTWl4dFB6QlpIeWNhaktlcTdKMVhqUTN4RmEwV3puRXJoRDJBRnR6bkpLeWdWZ2c0ZlpoYkVCSkt5N0FPVitSK2wxWi9NOXVpVnhkeXNBQkNFd2NlbG01WFVROUROSk9zSkVtS1VPYy96VmNEamN3TXlMcDJTU2swUjRaQ3dsUGdnbHF4V1lMQjhiN01pcndLVVBhL3N3VGU0RnAzRzNrckk5elJUc1pVWnQ2ODhXY1VSbGh1VURsbkdtSkZzSW9qZnNVRm0wbHd5bnZzZmdsSzMyM3RNUktuaXJqUWJQZTY2K1A5S044a3lWSWVEL04yK3lBcHpjRENteDdJdS8vek5aVDV2NWYvR3NpMll0alRnbUlLM2NyNlRXaDdzY2d3Z1hCWUVGWnY2a0FPRmFIZDNrVHUxSUY0WTYveE9oWlg5eHliaWMiLCJtYWMiOiJhMmY0MDUzZTY0Y2E4NjFmMDdlMmU5M2NlYmQ1N2JkNzE1NTg0YWJhNDQwZTUyMjhlNDE2ODI1YjM0OWY3YmUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356636872\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-95581028 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-95581028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-278226359 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkV1WXhxN0U0Y2g0bFRyWGdyREIvTUE9PSIsInZhbHVlIjoiell2ajNScE9vdDl1aG5LYmtDSEhpdVBqeUk3U3BZQ3AxbUcvaHZDcHlVK3ZrU1FQTXM5N3V1K3MxbVpwUmJyd1R2NGFGcVVxKzhDaEI4eFZJV1h5SDh5clVlK0hsbjFRcmlWY1dqOEViSEJTaGhKWk5xenp5ejhFSWNsUC93QlJ4VFE5Yjc0TEUzdHgzeDE4bEN4eG83TGRhRjJPUzRPTWZMZzVmL3hyNjl2VEo5enpaRXdIUFlRQVR0NUpGbHkxZ3JVcFphL1A2QzFab0tZSER3bmZsSS9hT1dqNmQvcFlwbkpFNWJ0dG1FcjJWbTZTaG9LN09DWE1rRU5DNHNqY1RCOTVuN2lkNGxPWVBDdWwzT1hFTGF0ZFlBK2pqblJ0bmdNTWZrVHQ0NUtpeWRDVjZqUW1tbzJDMmR5L0F6cFRmREZTNUExZ1cyOGhnclgvOWxvcFcyQ3E1OUY5alRFOS9nTmdqWlJyZnZ1aVNEenlrK0RSMFJyUmxmRXVhZDcvWGs4ZDR4Y0ZQV2NLTlhsNzRsSzM4d0tOK2FIWUtmL3ZGS2VKSGtHeEg1V3l4eVJIalNmLzRYQ0FCRjU0SlUzYnQ4WXQ3cU1DZXZ3VGRkQ2R0STFhUGF3WDdnVkV4RW1mSUh0TlRFU0xIcXBGak9NUEY3ekpVQnV4VE90aTZVMzkiLCJtYWMiOiI1ZTBmMDdhNjRmNGEzZGNjNzBkNzEwZjMwNWZlMDQ1ODJmMTE5ZjEzNjdlYjZiYTI4MGQwMjg3NmNmODM5MTYwIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlptSXpLaHV6VCtDZXdlSm1aZFk5K3c9PSIsInZhbHVlIjoiaHRFRUhJejBWRE14dUFoNjFNbGpwdldrT2xSRk1oWmVQQjBxNkVtZlJ5NFVFTWd0WlgvQ1BRcitCSjk5Ujc5cGFHR1Yyc1JOdzE5cEtXcUc2aUl4Y0xUUG5wVEk1VWFqUW5wVHdxYXh4amQwMCt1Vnh1QU5GbnQ1R1hqTXRIN250eGlNemJpcmhlLzJkU2U1MzFZSGRBc1l2YzNrd2E3NWFxSG84QzR3ejY1amo4OW1ZcjJUUDhsQVRvTDArY1hiSjhQS2Y0bEJjN2I1bkRCd3ByVUtmNHBmUHlYOWd1Z1RVRFA1aklBd3ZaU3M0REpFRjRuZ2VnRXY0MEt6UUlJVVp6QmxqRDlIaC9JL1BBb2p4SXJDbzdkN3VWZUV1dFRtMHpXTUV5ajZ2SlkrRTdhWXJuSTk4QmR0RGltcE1JZk5wUWFPMU1jVTZ2aWI2SnN0cS8yR05YL0RqaWFReTZ6enU3Q1lnTjhHWVZJUmRRL2xOTEJ3akNzbkMvZFQ1eTY5N0prNWg0YlFHNS9MVXJLZElnNnlJTHYzVEE0enZSUnZxTFhmOXRVcWhQRklqWmsrZGJjYjFuNVUydGs2OG83SlljMm0rWE85WHJPeGhwdG4rZXZKL0RRd3dPM1lPamUxekxXWmw0L080NWtNaXhiZkJOM3NZMWV6bXRjdHRKajAiLCJtYWMiOiI0MGFjMTcyMTU0MGFjNzNiOWYwNzQyNTM4M2RiNzZlMDE1YWQzNGNlODcyM2NjNTc0NTM3NzMyNzFmZTA3MmJkIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkV1WXhxN0U0Y2g0bFRyWGdyREIvTUE9PSIsInZhbHVlIjoiell2ajNScE9vdDl1aG5LYmtDSEhpdVBqeUk3U3BZQ3AxbUcvaHZDcHlVK3ZrU1FQTXM5N3V1K3MxbVpwUmJyd1R2NGFGcVVxKzhDaEI4eFZJV1h5SDh5clVlK0hsbjFRcmlWY1dqOEViSEJTaGhKWk5xenp5ejhFSWNsUC93QlJ4VFE5Yjc0TEUzdHgzeDE4bEN4eG83TGRhRjJPUzRPTWZMZzVmL3hyNjl2VEo5enpaRXdIUFlRQVR0NUpGbHkxZ3JVcFphL1A2QzFab0tZSER3bmZsSS9hT1dqNmQvcFlwbkpFNWJ0dG1FcjJWbTZTaG9LN09DWE1rRU5DNHNqY1RCOTVuN2lkNGxPWVBDdWwzT1hFTGF0ZFlBK2pqblJ0bmdNTWZrVHQ0NUtpeWRDVjZqUW1tbzJDMmR5L0F6cFRmREZTNUExZ1cyOGhnclgvOWxvcFcyQ3E1OUY5alRFOS9nTmdqWlJyZnZ1aVNEenlrK0RSMFJyUmxmRXVhZDcvWGs4ZDR4Y0ZQV2NLTlhsNzRsSzM4d0tOK2FIWUtmL3ZGS2VKSGtHeEg1V3l4eVJIalNmLzRYQ0FCRjU0SlUzYnQ4WXQ3cU1DZXZ3VGRkQ2R0STFhUGF3WDdnVkV4RW1mSUh0TlRFU0xIcXBGak9NUEY3ekpVQnV4VE90aTZVMzkiLCJtYWMiOiI1ZTBmMDdhNjRmNGEzZGNjNzBkNzEwZjMwNWZlMDQ1ODJmMTE5ZjEzNjdlYjZiYTI4MGQwMjg3NmNmODM5MTYwIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlptSXpLaHV6VCtDZXdlSm1aZFk5K3c9PSIsInZhbHVlIjoiaHRFRUhJejBWRE14dUFoNjFNbGpwdldrT2xSRk1oWmVQQjBxNkVtZlJ5NFVFTWd0WlgvQ1BRcitCSjk5Ujc5cGFHR1Yyc1JOdzE5cEtXcUc2aUl4Y0xUUG5wVEk1VWFqUW5wVHdxYXh4amQwMCt1Vnh1QU5GbnQ1R1hqTXRIN250eGlNemJpcmhlLzJkU2U1MzFZSGRBc1l2YzNrd2E3NWFxSG84QzR3ejY1amo4OW1ZcjJUUDhsQVRvTDArY1hiSjhQS2Y0bEJjN2I1bkRCd3ByVUtmNHBmUHlYOWd1Z1RVRFA1aklBd3ZaU3M0REpFRjRuZ2VnRXY0MEt6UUlJVVp6QmxqRDlIaC9JL1BBb2p4SXJDbzdkN3VWZUV1dFRtMHpXTUV5ajZ2SlkrRTdhWXJuSTk4QmR0RGltcE1JZk5wUWFPMU1jVTZ2aWI2SnN0cS8yR05YL0RqaWFReTZ6enU3Q1lnTjhHWVZJUmRRL2xOTEJ3akNzbkMvZFQ1eTY5N0prNWg0YlFHNS9MVXJLZElnNnlJTHYzVEE0enZSUnZxTFhmOXRVcWhQRklqWmsrZGJjYjFuNVUydGs2OG83SlljMm0rWE85WHJPeGhwdG4rZXZKL0RRd3dPM1lPamUxekxXWmw0L080NWtNaXhiZkJOM3NZMWV6bXRjdHRKajAiLCJtYWMiOiI0MGFjMTcyMTU0MGFjNzNiOWYwNzQyNTM4M2RiNzZlMDE1YWQzNGNlODcyM2NjNTc0NTM3NzMyNzFmZTA3MmJkIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-278226359\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-660762952 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660762952\", {\"maxDepth\":0})</script>\n"}}