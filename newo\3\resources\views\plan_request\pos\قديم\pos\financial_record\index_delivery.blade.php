{{-- @php
    $is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');
@endphp --}}
@extends('layouts.admin')
@section('page-title')
    {{ __('Financial record') }}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('Financial record') }}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('content')
    <div id="printableArea">
        <div class="mt-3 row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                    <tr>
                                        <th>{{ __('Opening Balance') }}</th>
                                        <th>{{ __('Current Cash') }}</th>
                                        <th>{{ __('Overnetwork Cash') }}</th>
                                        <th>{{ __('Delivery Cash') }}</th>
                                        <th>{{ __('Total Cash') }}</th>
                                        <th>{{ __('Deficit') }}</th>
                                        <th>{{ __('Received Advance') }}</th>
                                        <th>{{ __('Created At') }}</th>
                                    </tr>
                                </thead>

                                <tbody>
                                    @if ($deliveryFinancialRecord)
                                        <tr>
                                            <td>{{ 'N/A' }}</td>
                                            <td>{{ 'N/A' }}</td>
                                            <td>{{ $deliveryFinancialRecord->overnetwork_cash ?? '0.00' }}</td>
                                            <td>{{ $deliveryFinancialRecord->delivery_cash ?? '0.00' }}</td>
                                            <td>{{ 'N/A' }}</td>
                                            <td>{{ 'N/A' }}</td>
                                            <td>{{ 'N/A' }}</td>
                                            <td>{{ $deliveryFinancialRecord->created_at }}</td>
                                        </tr>
                                    @else
                                        <tr>
                                            <td colspan="7" class="text-center text-dark">
                                                <p>{{ __('No Data Found') }}</p>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- payment voucher --}}
    <div class="row mt-5">
        <h3 class="mb-4">{{ __('Payment voucher') }}</h3>
        <div class="col-md-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table payment-voucher-table">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Payment Amount') }}</th>
                                    <th>{{ __('Issue') }}</th>
                                    <th>{{ __('Payed to') }}</th>
                                    <th>{{ __('Voucher Status') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('Date') }}</th>
                                </tr>
                            </thead>

                            <tbody>

                                @forelse ($paymentVouchers as $payment)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('payment.voucher.show', $payment->id) }}"
                                                class="btn {{ $payment->created_by == Auth::user()->id ? 'btn-outline-primary' : 'btn-outline-warning' }}">{{ $payment->custome_id }}</a>

                                        </td>
                                        <td> {{ $payment->payment_amount }} </td>
                                        <td>{{ $payment->creator?->name }}</td>
                                        <td>{{ $payment->payTo?->name }}</td>
                                        <td>
                                            <span
                                                class="purchase_status badge {{ $payment->status == 'pending' ? 'bg-warning' : 'bg-success' }}  p-2 px-3 rounded">{{ $payment->status == 'pending' ? __('Waiting') : __('Accepted') }}</span>
                                        </td>
                                        <td>
                                            <span>{{ ucwords(str_replace('_', ' ', $payment->payment_method)) }}</span>
                                        </td>
                                        <td>
                                            <span>{{ $payment->date }}</span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center text-dark">
                                            <p>{{ __('No Data Found') }}</p>
                                        </td>
                                    </tr>
                                @endforelse

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- receipt voucher --}}
    <div class="row mt-5">
        <h3 class="mb-4">{{ __('Receipt voucher') }}</h3>
        <div class="col-md-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table receipt-voucher-table">
                            <thead>
                                <tr>
                                    <th>{{ __('ID') }}</th>
                                    <th>{{ __('Payment Amount') }}</th>
                                    <th>{{ __('Issue') }}</th>
                                    <th>{{ __('Recipient from') }}</th>
                                    <th>{{ __('Voucher Status') }}</th>
                                    <th>{{ __('Payment Method') }}</th>
                                    <th>{{ __('Date') }}</th>
                                </tr>
                            </thead>

                            <tbody>

                                @forelse ($receiptVouchers as $receipt)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('receipt.voucher.show', $receipt->id) }}"
                                                class="btn {{ $receipt->created_by == Auth::user()->id ? 'btn-outline-primary' : 'btn-outline-warning' }}">{{ $receipt->custome_id }}</a>

                                        </td>
                                        <td> {{ $receipt->payment_amount }} </td>
                                        <td>{{ $receipt->creator?->name }}</td>
                                        <td>{{ $receipt->receiptFrom?->name }}</td>
                                        <td>
                                            <span
                                                class="purchase_status badge {{ $receipt->status == 'pending' ? 'bg-warning' : 'bg-success' }} p-2 px-3 rounded">{{ $receipt->status == 'pending' ? __('Waiting') : __('Accepted') }}</span>
                                        </td>
                                        <td>
                                            <span>{{ ucwords(str_replace('_', ' ', $receipt->payment_method)) }}</span>
                                        </td>
                                        <td>
                                            <span>{{ $receipt->date }}</span>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center text-dark">
                                            <p>{{ __('No Data Found') }}</p>
                                        </td>
                                    </tr>
                                @endforelse

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('custom-js')
    <script>
        $(document).ready(function() {
            const receiptDataTable = new simpleDatatables.DataTable(".receipt-voucher-table");
            const paymentDataTable = new simpleDatatables.DataTable(".payment-voucher-table");
        });
    </script>
@endsection
