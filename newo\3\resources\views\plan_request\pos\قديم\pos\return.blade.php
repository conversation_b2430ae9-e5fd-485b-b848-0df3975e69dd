@extends('layouts.admin')
@section('page-title')
    {{__('Return Products')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('pos.report')}}">{{__('POS Summary')}}</a></li>
    <li class="breadcrumb-item">{{ AUth::user()->posNumberFormat($pos->id) }}</li>
    <li class="breadcrumb-item">{{__('Return Products')}}</li>
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row mt-2">
                        <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12">
                            <h4>{{__('Return Products')}}</h4>
                        </div>
                        <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12 text-end">
                            <h4 class="invoice-number">{{ Auth::user()->posNumberFormat($pos->id) }}</h4>
                        </div>
                        <div class="col-12">
                            <hr>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-5">
                            <small class="font-style">
                                <strong>{{__('Billed To')}} :</strong><br>
                                @if(!empty($customer->billing_name))
                                    {{!empty($customer->billing_name)?$customer->billing_name:''}}<br>
                                    {{!empty($customer->billing_address)?$customer->billing_address:''}}<br>
                                    {{!empty($customer->billing_city)?$customer->billing_city:'' .', '}}<br>
                                    {{!empty($customer->billing_state)?$customer->billing_state:'',', '}},
                                    {{!empty($customer->billing_zip)?$customer->billing_zip:''}}<br>
                                    {{!empty($customer->billing_country)?$customer->billing_country:''}}<br>
                                    {{!empty($customer->billing_phone)?$customer->billing_phone:''}}<br>
                                @else
                                    -
                                @endif
                            </small>
                        </div>
                        <div class="col-4">
                            @if(App\Models\Utility::getValByName('shipping_display')=='on')
                                <small>
                                    <strong>{{__('Shipped To')}} :</strong><br>
                                        @if(!empty($customer->shipping_name))
                                        {{!empty($customer->shipping_name)?$customer->shipping_name:''}}<br>
                                        {{!empty($customer->shipping_address)?$customer->shipping_address:''}}<br>
                                        {{!empty($customer->shipping_city)?$customer->shipping_city:'' . ', '}}<br>
                                        {{!empty($customer->shipping_state)?$customer->shipping_state:'' .', '}},
                                        {{!empty($customer->shipping_zip)?$customer->shipping_zip:''}}<br>
                                        {{!empty($customer->shipping_country)?$customer->shipping_country:''}}<br>
                                        {{!empty($customer->shipping_phone)?$customer->shipping_phone:''}}<br>
                                    @else
                                    -
                                    @endif
                                </small>
                            @endif
                        </div>
                        <div class="col-3">
                            <div class="d-flex align-items-center justify-content-end">
                                <div class="me-4">
                                    <small>
                                        <strong>{{__('Issue Date')}} :</strong>
                                        {{\Auth::user()->dateFormat($pos->created_at)}}<br><br>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <form action="{{ route('pos.return.process', $pos->id) }}" method="POST">
                        @csrf
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="table-responsive mt-3">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th class="text-dark" >#</th>
                                            <th class="text-dark">{{__('Items')}}</th>
                                            <th class="text-dark">{{__('Quantity Sold')}}</th>
                                            <th class="text-dark">{{__('Quantity to Return')}}</th>
                                            <th class="text-dark">{{__('Price')}}</th>
                                            <th class="text-dark">{{__('Tax')}}</th>
                                            <th class="text-dark">{{__('Return Reason')}}</th>
                                        </tr>
                                        </thead>
                                        @php
                                            $totalQuantity=0;
                                            $totalRate=0;
                                            $totalTaxPrice=0;
                                            $totalDiscount=0;
                                            $taxesData=[];
                                        @endphp
                                        @foreach($iteams as $key =>$iteam)
                                            @php
                                                $totalQuantity+=$iteam->quantity;
                                                $totalRate+=$iteam->price;
                                                $totalDiscount+=$iteam->discount;
                                            @endphp
                                            <tr>
                                                <td>{{$key+1}}</td>
                                                <td>{{!empty($iteam->product())?$iteam->product()->name:''}}</td>
                                                <td>{{$iteam->quantity}}</td>
                                                <td>
                                                    <input type="hidden" name="item_id[]" value="{{$iteam->id}}">
                                                    <input type="number" class="form-control return-qty" name="return_qty[]" min="0" max="{{$iteam->quantity}}" value="0">
                                                </td>
                                                <td>{{\Auth::user()->priceFormat($iteam->price)}}</td>
                                                <td>
                                                    @if(!empty($iteam->tax))
                                                        <table>
                                                            @php
                                                                $totalTaxRate = 0;
                                                                $totalTaxPrice = 0;
                                                                $taxes = App\Models\Utility::tax($iteam->tax);
                                                            @endphp
                                                            @foreach($taxes as $tax)
                                                                @php
                                                                    $taxPrice=App\Models\Utility::taxRate($tax->rate,$iteam->price,$iteam->quantity);
                                                                    $totalTaxPrice+=$taxPrice;
                                                                @endphp
                                                                <tr>
                                                                    <span class="badge bg-primary">{{$tax->name .' ('.$tax->rate .'%)'}}</span> <br>
                                                                </tr>
                                                            @endforeach
                                                        </table>
                                                    @else
                                                        -
                                                    @endif
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="return_reason[]" placeholder="{{__('Return Reason')}}">
                                                </td>
                                            </tr>
                                        @endforeach
                                    </table>
                                </div>
                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="return_note">{{__('Return Note')}}</label>
                                            <textarea class="form-control" name="return_note" id="return_note" rows="3"></textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-4">
                                    <div class="col-md-12 text-end">
                                        <button type="submit" class="btn btn-primary">{{__('Process Return')}}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        $('.return-qty').on('change', function() {
            var max = parseInt($(this).attr('max'));
            var val = parseInt($(this).val());
            if (val > max) {
                $(this).val(max);
                show_toastr('Error', '{{__("Return quantity cannot exceed sold quantity")}}', 'error');
            }
        });
    });
</script>
@endpush
