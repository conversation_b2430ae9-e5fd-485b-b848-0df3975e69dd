<?php
/**
 * تشخيص مسار معالجة الدفع في نظام POS
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 تشخيص معالجة الدفع في نظام POS</h1>";
echo "<hr>";

// فحص الجلسة
session_start();
echo "<h2>🔐 فحص الجلسة:</h2>";
if (isset($_SESSION)) {
    echo "<p style='color: green;'>✅ الجلسة تعمل</p>";
    
    if (isset($_SESSION['pos'])) {
        echo "<p>🛒 بيانات POS في الجلسة: " . count($_SESSION['pos']) . " منتج</p>";
        echo "<details><summary>عرض بيانات السلة</summary>";
        echo "<pre>" . print_r($_SESSION['pos'], true) . "</pre>";
        echo "</details>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد بيانات POS في الجلسة</p>";
    }
    
    if (isset($_SESSION['payment_processed'])) {
        echo "<p>💳 حالة الدفع: " . ($_SESSION['payment_processed'] ? 'تم المعالجة' : 'لم يتم المعالجة') . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ مشكلة في الجلسة</p>";
}

// فحص قاعدة البيانات
echo "<h2>🗄️ فحص قاعدة البيانات:</h2>";
try {
    $envFile = __DIR__ . '/../.env';
    if (file_exists($envFile)) {
        $envContent = file_get_contents($envFile);
        preg_match('/DB_HOST=(.*)/', $envContent, $hostMatch);
        preg_match('/DB_DATABASE=(.*)/', $envContent, $dbMatch);
        preg_match('/DB_USERNAME=(.*)/', $envContent, $userMatch);
        preg_match('/DB_PASSWORD=(.*)/', $envContent, $passMatch);
        
        $host = trim($hostMatch[1] ?? '127.0.0.1');
        $database = trim($dbMatch[1] ?? '');
        $username = trim($userMatch[1] ?? 'root');
        $password = trim($passMatch[1] ?? '');
    } else {
        throw new Exception('ملف .env غير موجود');
    }
    
    $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ الاتصال بقاعدة البيانات ناجح</p>";
    
    // فحص الجداول المطلوبة
    $tables = ['pos', 'pos_payments', 'pos_products', 'shifts', 'financial_records'];
    foreach ($tables as $table) {
        try {
            $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p>✅ جدول $table: $count سجل</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ مشكلة في جدول $table: " . $e->getMessage() . "</p>";
        }
    }
    
    // فحص الورديات المفتوحة
    echo "<h3>📅 الورديات المفتوحة:</h3>";
    $stmt = $pdo->query("SELECT * FROM shifts WHERE closed_at IS NULL");
    $openShifts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($openShifts) > 0) {
        echo "<p style='color: green;'>✅ توجد " . count($openShifts) . " وردية مفتوحة</p>";
        foreach ($openShifts as $shift) {
            echo "<p>🕐 وردية رقم {$shift['id']} - مستودع {$shift['warehouse_id']} - بدأت {$shift['created_at']}</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ لا توجد ورديات مفتوحة</p>";
    }
    
    // فحص آخر فاتورة POS
    echo "<h3>🧾 آخر فاتورة POS:</h3>";
    $stmt = $pdo->query("SELECT p.*, pp.payment_type, pp.amount FROM pos p LEFT JOIN pos_payments pp ON p.id = pp.pos_id ORDER BY p.id DESC LIMIT 1");
    $lastPos = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($lastPos) {
        echo "<p>📄 آخر فاتورة: رقم {$lastPos['pos_id']} بتاريخ {$lastPos['pos_date']}</p>";
        echo "<p>💰 نوع الدفع: " . ($lastPos['payment_type'] ?? 'غير محدد') . "</p>";
        echo "<p>💵 المبلغ: " . ($lastPos['amount'] ?? 'غير محدد') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ لا توجد فواتير POS</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
}

// فحص المسارات
echo "<h2>🛣️ فحص المسارات:</h2>";
$routes = [
    'pos.pos-payment-type' => '/pos-payment-type',
    'pos.data.store' => '/pos/data/store',
    'pos.billtype' => '/pos-payment-type'
];

foreach ($routes as $name => $url) {
    echo "<p>🔗 $name: $url</p>";
}

// محاكاة طلب الدفع
echo "<h2>🧪 محاكاة طلب الدفع:</h2>";
echo "<button onclick='simulatePayment()' style='padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>محاكاة طلب دفع</button>";
echo "<div id='payment-result' style='margin-top: 10px;'></div>";

// فحص ملفات Laravel
echo "<h2>📁 فحص ملفات المعالجة:</h2>";
$files = [
    '../app/Http/Controllers/FinancialRecordController.php' => 'معالج الدفع الرئيسي',
    '../app/Http/Controllers/PosController.php' => 'معالج POS',
    '../resources/views/pos/bill_type.blade.php' => 'شاشة اختيار نوع الدفع',
    '../resources/views/pos/payment_success.blade.php' => 'شاشة نجاح الدفع'
];

foreach ($files as $file => $description) {
    $exists = file_exists(__DIR__ . '/' . $file);
    $status = $exists ? '✅' : '❌';
    echo "<p>$status $description: " . ($exists ? 'موجود' : 'غير موجود') . "</p>";
}

?>

<script>
function simulatePayment() {
    const resultDiv = document.getElementById('payment-result');
    resultDiv.innerHTML = '⏳ جاري محاكاة طلب الدفع...';
    
    // بيانات محاكاة
    const paymentData = {
        payment_type: 'cash',
        total_price: 100.00,
        vc_name: 0,
        warehouse_name: 1,
        discount: 0,
        quotation_id: 0
    };
    
    // محاولة إرسال طلب للمسار الصحيح
    fetch('/pos-payment-type', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': 'test-token'
        },
        body: JSON.stringify(paymentData)
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        if (response.ok) {
            return response.text();
        } else {
            throw new Error('HTTP ' + response.status + ': ' + response.statusText);
        }
    })
    .then(data => {
        resultDiv.innerHTML = '✅ استجابة الخادم:<br><pre>' + data.substring(0, 500) + '...</pre>';
        resultDiv.style.background = '#d4edda';
        resultDiv.style.padding = '10px';
        resultDiv.style.borderRadius = '5px';
        resultDiv.style.color = '#155724';
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ خطأ في المحاكاة: ' + error.message;
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.padding = '10px';
        resultDiv.style.borderRadius = '5px';
        resultDiv.style.color = '#721c24';
        
        console.error('Payment simulation error:', error);
    });
}

// فحص تلقائي عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 صفحة تشخيص الدفع محملة');
    
    // فحص وجود jQuery
    if (typeof $ !== 'undefined') {
        console.log('✅ jQuery متوفر');
    } else {
        console.log('❌ jQuery غير متوفر');
    }
    
    // فحص CSRF token
    const csrfMeta = document.querySelector('meta[name="csrf-token"]');
    if (csrfMeta) {
        console.log('✅ CSRF Token موجود:', csrfMeta.content);
    } else {
        console.log('❌ CSRF Token غير موجود');
    }
});
</script>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa;
}
h1, h2, h3 { 
    color: #333; 
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}
details {
    margin: 10px 0;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 5px;
}
pre {
    background: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    max-height: 200px;
}
button:hover { 
    background: #0056b3 !important; 
}
</style>
