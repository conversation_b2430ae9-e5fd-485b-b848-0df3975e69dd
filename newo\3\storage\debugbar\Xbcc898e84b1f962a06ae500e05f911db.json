{"__meta": {"id": "Xbcc898e84b1f962a06ae500e05f911db", "datetime": "2025-06-17 12:18:43", "utime": **********.459252, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162722.881823, "end": **********.459278, "duration": 0.5774550437927246, "duration_str": "577ms", "measures": [{"label": "Booting", "start": 1750162722.881823, "relative_start": 0, "end": **********.368856, "relative_end": **********.368856, "duration": 0.4870328903198242, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.368869, "relative_start": 0.4870460033416748, "end": **********.459281, "relative_end": 2.86102294921875e-06, "duration": 0.09041190147399902, "duration_str": "90.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45531776, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01935, "accumulated_duration_str": "19.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.415336, "duration": 0.01706, "duration_str": "17.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 88.165}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.444314, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 88.165, "width_percent": 4.806}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.448893, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "ty", "start_percent": 92.972, "width_percent": 7.028}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-2052222801 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2052222801\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-548921176 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-548921176\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263064506 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263064506\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1099040225 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1938 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=1ta1y4p%7C1750157552093%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRsRi96Slk1amtPNHlJVE5oQWlzZWc9PSIsInZhbHVlIjoiV002WHFlaXRYelRIQ3J5OVZka3VwaEl3S1pnOGFRQkFNOUNqQjdGNmRVdWV2QlZUenZzVFBNTWVyR0FkNEZxd1pBemVZcGMrSGhaOGFPbzFaMFFzNVNlVjhBYkZPZzlZUWw5VTJwQll1Um1ZdXNXS05Sb1pabVNsTlQ1aVBBeERLTTFlQnkrbjByZUpCb3J6RTR4K3dMMS9QNXMxNXlBTU5XMDZmZVFaWk9NZWVGQVU3cEJOVlhyWHVSTzUxRFlHTjlNUFZiVVQ4QzdER0hna2cybUFKVEtIR3JJWHJ3eXBEWmxBenh2TkRQME9xQXpvSUxtTStoeFBiZ0FreXpGZ3VVeURTb3JlSWhQcFRTV05WaWpmK1cvd05lU1U3aWpmQjQwT2tqZkUyb0EwNk1ZVm9OZzVsdFYvYk5NbHBDOWdXYUFZTlVQMllpbUhlNk1LS3p2NGNyT2RFTVRPUHVtWE1ORDJQZCt6SHQ5dTFxK25KK1ZJdUlyUkFRTE56QkIzY0lzTzBudEF2VG5zekhYZmhHMFR5NGwxWjhRMURYcXcwZnV6ZVJrVElpNE5Sc2tMTnlGbWdYTHFkUTJpTk1DM3FiMVRYcEwyK3VGQkI0WUtGUHVabEZPUkhRd1ZTUkhaUFEvY3h4STZxaDNqc0paQmVBYmEzOGZjOTc4QXRyZ1QiLCJtYWMiOiJjM2E0ZDE3ZDcwMmE3MjkyYTQ4OTBjNTAwMGRlYTQ1MGM2OGE0NGJkMjVkMjdkZjE0ZDY1YzA1YWFiNmMzZmJmIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6InFFeGZqUkphdURscFF0OG8vVGJHRGc9PSIsInZhbHVlIjoiRnJzZU4rKytud09iSzB6SjF5dGJBYk9kcmdtRFdFdWI2K2JEbmVkM000YWEzL1crTHBjV25uNk85bTZGVjI2RmhCTE9kVFg0eVU0d0hIbFJkVlNRbmU4QitUOFVMRThoTHFRVmZTSndHSVZRZ29ZdWZleVNpM1pHSTRhVVFIVDJNN0tZWVVESUJ4Mlo4akhlekhTVEdLaVRQTDJOYnhQMC84UHZld2I0QWtQZnRqZEVOSThJYzhDWmluUmc2UVRpVTlpenc2VDB5VktoVGVWVnc0ZDlibmoycXJXQUdBS0haNmpNSUZ0dWFydUVIV2lLZ2xMdG9OWUJNOThBMXlVdWlrcnJ0K01lSDBzcW5SR1dMMTlRbVl1SlcrcHNCRitYWWJPNTY4ZUE1RnlkR3B3UWE5TlRyelRxOGNSNEZURTRuM05vbXNQL1ZzYXNtWE9hS0JKS0tVSFNNTzNQSmM5VXN5VVNPQkN0dHdwN0ZZVUJHRStnQWtpUTREQWo0OGJ4NjBRRVpRSGg4OG11bC9MR1hkUGR3cWhzM2VSMEtPQzREVTJ4ellJcG9IRDJDZFB5NVJ1aUowTFFyWnkwbkZiWlZ5cnV6dFJpSUZHT0lCZ3lOMUR2bXI3RzFGQzVCNHl4ckdyQjVtQUNXTytsVEZjeEhCQUhUWnZ0enNIU3BWLy8iLCJtYWMiOiJiOGI1ZTQxZWI4YjJlMGMyOTdlZWVlMmFjODNjZTE4NzM5YzQ1NTg3MDBlZTFkMDI2NjIyNDE2NDIyZDJlNDIzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099040225\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-273707410 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273707410\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:18:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ4RUZzLzR5elVRcDhvaXhZbE5Ncmc9PSIsInZhbHVlIjoieFAwa28xU2lGUUpjTHdlTDNXQ2g4S2t0UEZLRk1Qd1cvUHRQRnpnRVpVQmRjWG9NOFdJU1cyL05GdC9GSmFFYnBuL3NoUnkrZ2c4ZWw3VTVRZXk4aEd5U1Jkall5K2dDZVFaQ0JhcHJGT05qY2MrVHpOWUV6dlhZdWVNYWNFTVJxSTNJOFF1elhaT3NoRTBscUN3N1NTOUlHMlNnRWRZZFYzc016c0J4SXVoQ0c3T3BaTWtZYVZ6ZXFYM0w1TTlYYWJMZjZuSVRwMjlCKzBUcGgzL2Fscm1CZ2dRZkdyTEZiUUwzamhXeFljRXBvU3Q4S3Y5N0NjeXZVaXdzRm4xOUJzNk95ZDdkNGxTUGtNb1BWd1pMQUJQYVcrNVNTM0NNRnJMcGhJUGNNeEl5RmtRVzVabGk3cFU4bTdCUlhGWjZLUzNaOGI0Mm9OTWR0K0ExdmZJS044QUVDWjQrc0hjMVdkNGE1bUFGU2xvaVRLK1F5QWF2QUloL2xEVDVoMWlLRmd3Vm9GTDVaZmcwU000bUErNVFpRlM0OUZpR1ltaWQxK1dEL2xFZEUrZkZ1QnV1STNDaW1WQzZ0dUIwNTkvWm9uTm9ycUQ3WEgvMEJwK0FJb1BySzJBeFdWTWQ3UFI5NkpyNDY3ajFiWWpReUZ0MzI3YWNsQ21wcGpFRzdLLzQiLCJtYWMiOiIxOTIyYTMwMmFiZWQ2YWVhYjU1MTNiODA4OTE1NTZiODcyZjMyMzk2MTFlNDdiMTgxY2M1YjI1YTdjOWVhYjI4IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6ImdER0NnTWVQa05EUjlGNWN5eDJmUHc9PSIsInZhbHVlIjoicmZGRzRmUzQ4QkxTTUVzeHAwNTJ1MzdOcEliZHJaYWVHTU15TnVOUDA1NWVTeWxkRGdsNFp2MDNia0R4RlFJNWpqa0NNN213U0ROdHh0V0FYZ3hXOVp1dEx2dk0yNjhvdURmWEI0RVM3NGZJZW1NOW43eC9aK1ZING03S1dnenQ2b2VHQ2t3cjJwSWxoaWRHUVlyeE5vRkNGQ1BmeURVWUI1L1JuMjFJWDRMNjVub2dBNHR5N3ZEeXM1VnArRUpWKzh2c2Y2bG8rTDRmb285WndleHp4Y1BmN2xuZEVPNXQxRlc0bXJ5bDcrUWpOTGxDSWNvM3B3NGcwM0ZpNll3TWt0eW5RZ3hVeW1CZmE4VWhlbENtcldQdUppYktqRStCM0xJaUJJbkNZZmttK3czQ1FaeFVIbnBBckJMNitocXdWM0ozS3BsMTJDMEVDSk1EL2d3VlM3Sms0aWlsRUo5WGlQVnJocHpjbFlkR0NjZnpXMlFEVWx6cG5CM3M1T2ZWSE95bzBDTFNQcmNJTUROeDlCL2lwb3BuU1N3V2kxMnVwMlVyS0ZRZ1ZKY09vdm5KNDhFWnlWeERwdSt6OC8wQkRvV3p1OUcvMFRRdlNWUGtuRVQ2MUhXcTJrcitsY01tNisweWlUdFM4N2RxWjVBU2Y4ZThPSWdWanAzM0FHNEUiLCJtYWMiOiJlN2VjZTc1YWE1NmM1NmJkMGU2NWNhOTY4YWM1MWMyZDQyY2M5MGU1YzVjMTU3ZTRjYmE1NGY4ODQ3ZWI2MzUzIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:18:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ4RUZzLzR5elVRcDhvaXhZbE5Ncmc9PSIsInZhbHVlIjoieFAwa28xU2lGUUpjTHdlTDNXQ2g4S2t0UEZLRk1Qd1cvUHRQRnpnRVpVQmRjWG9NOFdJU1cyL05GdC9GSmFFYnBuL3NoUnkrZ2c4ZWw3VTVRZXk4aEd5U1Jkall5K2dDZVFaQ0JhcHJGT05qY2MrVHpOWUV6dlhZdWVNYWNFTVJxSTNJOFF1elhaT3NoRTBscUN3N1NTOUlHMlNnRWRZZFYzc016c0J4SXVoQ0c3T3BaTWtZYVZ6ZXFYM0w1TTlYYWJMZjZuSVRwMjlCKzBUcGgzL2Fscm1CZ2dRZkdyTEZiUUwzamhXeFljRXBvU3Q4S3Y5N0NjeXZVaXdzRm4xOUJzNk95ZDdkNGxTUGtNb1BWd1pMQUJQYVcrNVNTM0NNRnJMcGhJUGNNeEl5RmtRVzVabGk3cFU4bTdCUlhGWjZLUzNaOGI0Mm9OTWR0K0ExdmZJS044QUVDWjQrc0hjMVdkNGE1bUFGU2xvaVRLK1F5QWF2QUloL2xEVDVoMWlLRmd3Vm9GTDVaZmcwU000bUErNVFpRlM0OUZpR1ltaWQxK1dEL2xFZEUrZkZ1QnV1STNDaW1WQzZ0dUIwNTkvWm9uTm9ycUQ3WEgvMEJwK0FJb1BySzJBeFdWTWQ3UFI5NkpyNDY3ajFiWWpReUZ0MzI3YWNsQ21wcGpFRzdLLzQiLCJtYWMiOiIxOTIyYTMwMmFiZWQ2YWVhYjU1MTNiODA4OTE1NTZiODcyZjMyMzk2MTFlNDdiMTgxY2M1YjI1YTdjOWVhYjI4IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6ImdER0NnTWVQa05EUjlGNWN5eDJmUHc9PSIsInZhbHVlIjoicmZGRzRmUzQ4QkxTTUVzeHAwNTJ1MzdOcEliZHJaYWVHTU15TnVOUDA1NWVTeWxkRGdsNFp2MDNia0R4RlFJNWpqa0NNN213U0ROdHh0V0FYZ3hXOVp1dEx2dk0yNjhvdURmWEI0RVM3NGZJZW1NOW43eC9aK1ZING03S1dnenQ2b2VHQ2t3cjJwSWxoaWRHUVlyeE5vRkNGQ1BmeURVWUI1L1JuMjFJWDRMNjVub2dBNHR5N3ZEeXM1VnArRUpWKzh2c2Y2bG8rTDRmb285WndleHp4Y1BmN2xuZEVPNXQxRlc0bXJ5bDcrUWpOTGxDSWNvM3B3NGcwM0ZpNll3TWt0eW5RZ3hVeW1CZmE4VWhlbENtcldQdUppYktqRStCM0xJaUJJbkNZZmttK3czQ1FaeFVIbnBBckJMNitocXdWM0ozS3BsMTJDMEVDSk1EL2d3VlM3Sms0aWlsRUo5WGlQVnJocHpjbFlkR0NjZnpXMlFEVWx6cG5CM3M1T2ZWSE95bzBDTFNQcmNJTUROeDlCL2lwb3BuU1N3V2kxMnVwMlVyS0ZRZ1ZKY09vdm5KNDhFWnlWeERwdSt6OC8wQkRvV3p1OUcvMFRRdlNWUGtuRVQ2MUhXcTJrcitsY01tNisweWlUdFM4N2RxWjVBU2Y4ZThPSWdWanAzM0FHNEUiLCJtYWMiOiJlN2VjZTc1YWE1NmM1NmJkMGU2NWNhOTY4YWM1MWMyZDQyY2M5MGU1YzVjMTU3ZTRjYmE1NGY4ODQ3ZWI2MzUzIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:18:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}