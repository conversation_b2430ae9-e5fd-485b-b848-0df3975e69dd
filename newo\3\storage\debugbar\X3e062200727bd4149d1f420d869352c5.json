{"__meta": {"id": "X3e062200727bd4149d1f420d869352c5", "datetime": "2025-06-17 12:19:44", "utime": **********.359777, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750162783.767664, "end": **********.359804, "duration": 0.5921399593353271, "duration_str": "592ms", "measures": [{"label": "Booting", "start": 1750162783.767664, "relative_start": 0, "end": **********.266376, "relative_end": **********.266376, "duration": 0.49871206283569336, "duration_str": "499ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.266388, "relative_start": 0.49872398376464844, "end": **********.359807, "relative_end": 3.0994415283203125e-06, "duration": 0.09341907501220703, "duration_str": "93.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45164960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02228, "accumulated_duration_str": "22.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3052819, "duration": 0.02043, "duration_str": "20.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 91.697}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3375669, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 91.697, "width_percent": 5.79}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3467739, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 97.487, "width_percent": 2.513}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-365636295 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-365636295\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-809301401 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-809301401\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1913454390 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913454390\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1508107054 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162773418%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9XaU9Ua0lWRXFmZUJOVkRsZlErS0E9PSIsInZhbHVlIjoiWXY0aDJxZFJjZnZMUUtCK2R1LzlsS2pDS1RNZGNLcURJOHlrQzBCR0U2WmpNYzZkN1A2M1YzSTRiVDZhVzRDNGdGemtuTGtsTkJVRXlsdEJJOWw1aG00NGxuQkUyQWQxQWp4QVpBVmlkRG1pakFTNWY1R3NtcWpBZk9HSEFqNHZ0akorRDhRc1lORERFSkVLNWxTc1BQWEZWS0RqZ2dha1dqZ0JrN2MyT2dWSGNMV1hPZ01CYmJWazVvcWFCTEpZdXBsdkpWejhaUXFqQjdGZHlQdVM0OGtaRGVLRXh5eERsZWMwZ2tQWVoxMGgrTHlCbjFQcEZQWHppbXhBNWZQZ0h5anhpWkp3aG93QTNSdnB0YXcva0ExUXRDNTJTbTQxdEowM0MrcVpPNXJiMWlGclZLTnhDR2ErTXVFR1kzeS9oaW4wL1ZCQnpqRk5DRUNLUGNaWjlIQmRid1pCT0d0c3JzK2FNbXBQZTdYdFhxYTRvMnppN3JKbStlUHJjWE1ETnVuNHlubk1TYkdDRzJWUE9wZGVBZUxNZDZFbCtFWEVKRlB3SEZJM1cyWjVIYUN3VFBOV0RWeU1QS1FqR0tpNDIyeWZxcGdRbmtJTTdmbUZMbzhmNlp3QkMxL2xqRVpzMVFpZUk2NVVpSjVFTUI2ZG5KTUlVQjRQNkZ1WW9pVWEiLCJtYWMiOiI2N2Y1YTRlYTk3ZjkzNDlhNTEwZmEzYzRjZTVlNTAxZTMwNmE3NjcxNTc1MWJlNGRkMmMyODA4ZmEwYjA4ODYyIiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjFwYVJoMnNwL2RRSE5uRUhpSHNabFE9PSIsInZhbHVlIjoiK0ZWVmJuMzduaTQ1SDE2K0xoblo0dXJYWDJQRHpIMm1KTHlYRlRMOU1vcFRRRTFsUXpibEd1Zi9HYVNGMk5wS1R0bU4rMGNxZmlOTGIzTktNd0NDOXdwRytjVDhMNlhvZExwQ1ZVaFR0T2NydmZYc2xpSzRXUGFBcnIwdEh2VFkwNktXeUFCdjlrS3VJclloQk5rMGpsL1hZQW5rSUM0dUg4dzVDQ2JyY0pBNjhIeFVBUDgxdEc5T0Nyb3gyMTFxdVBGRmh3R2Nab3BodDlKdXpaL2lvdWRxOThvU3IvaW5QUE5QczY5cEJDNzBTSGJrWFh0R1ZNb0VRN0d2Q1IyRGo4end3dFBtaHZvaVpOazgzem5KSVY2TDN1c2VUSmd5cWd1cGt0Y0wwczNvK0R1eHUzaTkrVmlKdXBhc0VPRXhTWk0vcmtXRkxKZ0JNcjBHdkd2YnZoRnFFQ2NCdGZDRE1uMHN3RFZoRzVtYWt5TTVVbGN1Smxqc0xhRFp2YUhDN1hmK0tSdWpKb29RRnB0K1VyUi9GZmc3clhQby9yVlQ4L2pEWTJoRThOUWVYTUsxRXBMRDdsQXRCMEEzWlhTcWo5Uk14T1FSd1hRdE90U1NVSlJROC9vcU9uY1orMjVaNUlURTRBLyt6YytjZTFKZjlxOEVPc3V5aXV2WTU2blAiLCJtYWMiOiIyMjE5MGQwMTUwZTViY2QyMTBjODdiMjhjZjI4ZWRlMTJiMDM1MjUzOTZjMmIxODQ0ZDI5Yzg2MzY1MzQ4MTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508107054\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-24286205 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24286205\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1976645934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ0YkJqUG5LdnJXQStEQlV4Y3hZa2c9PSIsInZhbHVlIjoieHRsV2JRWDhaQmcvR3VXK0tFY01mQ3htNkpydExYdGtrRTdOZ2R1d2lKdjEvckdWSjd5bmVKQzBmZ1NCbHdpQXJsZjYvQjZWdWFWRXptRHRMOGNwYmcyMytLanJQRDVSakxoRy9reE0vNjBoSEpNcEZ4YUI0SFVodkVWV2M4UFF6UG04ZGVaSlJXaWtZRjVCc2hFMkRPNXMzckkvbGdPckY2QjBDS0JyYk1wQS9sZnpjNjd0WUNTbVEyeDZyKzUzR0hGa3ZQQjNwMXlUSzBVeWFwNTlZS2srendzTVdpTkc2KzczVTBDRjVKM2NnUmRkYlAwL1VmREkrb2tTZ2FpNmxFOEYrTk92V0ZJVXMwQzRTeE9mazNvYU1qdnE3NU1MdlNlVHFwODFrbkxUbUNEcWpSZ1Y5UHNMcTYrVGhvMHY5cVJST2o5M0cyR2tWTkpJc256WFU0cUxZZFpQZGRneWgxQXJQNHptSVJRaVNxUDhIZmNwYktDRGMwSHJhdlpiaHY1UnpyL213aFdnL0YzSWFrRmVpTDc5WGp3QjBVYVdpL2VTZTBrNVB4TzJwaDRjZjI1dHJERC9GR3lhOEJQbGdKRm00ZUdCQzM1Si9WRnU2TWNWQkhnVXhPRklEU2Z4ZVZjMWo4M2crMC8ySWZ3QXFHTW9sajhzdThVakpYRTUiLCJtYWMiOiIzMmI5NDNmNDU5OTNjZWE4MjczZmEzMzEwZWIxZDEwYjhhODIwNjhiNTk1MjQyYjU3MzliMjI0ZTk4M2ZjNjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlNiTHQvZWlITmpBeVRCRzRwQmZGMUE9PSIsInZhbHVlIjoiZEdqSWVDUnlmMkt2ZTNJRFpYQ2pTTjRBcjdQSTlBU0VnaEJncGhVQmF5TFRVQS9tWG9kQ0hJU3RJbFp1S2RSdXRqa01BcWVDRTVhcHNBbXo3K2tBbHRJUVNOZkxWRytRdG55TFJlLzgzOVkyMktKWlRHTk1LZk9hYWVzczIyNEtvcTB5NkZ6eWhlUjVHcHRmYWhzTDNSRjNVQjRNVVFOampsbHIvR1BySHRXM3lyV2ZNNG1yMVlraFNlY0VIS3VoSUNlU0U4ZkhROGZEZE1rSWtqOVVGa2lvTGh0ZVdaS29UR20zeVpueTBRUzZKR0RNck83TDM4dzhmUUxTQlBnVkhURllmUHRFM09QQmtnYllsVmhCZXhMbmdxcGxzd091RE5ZZTRvYTczVHdhSUNPQjZpYzNlMnJqaUMvbnNiL0grdTd5ci94VW14Nlo3VzhtTUx3ZVhVS2ZkalVGd2hKdFd5QTJUcTFxcjYxanB1Q0h3eXNlNnIrVXJlN2lsU0FWUHpxQkJZMXhBK2d2OUY5RXE5MW1ENkk5WlZsZWpRTGZramdvM25iQ2xBaTY3bTFtNUJGWVhyT202cVMrV01VMTVLUjBRQml0WFYvVGVHWmVMYlhNeWt0Q0hEMVVwQUQxaWtUdTNnNHBaeURtZ1JqQkllVE45a0RBRmhwVVdPMGYiLCJtYWMiOiI1OWU5NmY5ZDViM2Y3MGFhMDU2Nzg0YjI0NmMzMzg5ZDRjZDZhNGYyYzFiZjQyYzk4YWJjZWYxZjUxODg4YjI1IiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ0YkJqUG5LdnJXQStEQlV4Y3hZa2c9PSIsInZhbHVlIjoieHRsV2JRWDhaQmcvR3VXK0tFY01mQ3htNkpydExYdGtrRTdOZ2R1d2lKdjEvckdWSjd5bmVKQzBmZ1NCbHdpQXJsZjYvQjZWdWFWRXptRHRMOGNwYmcyMytLanJQRDVSakxoRy9reE0vNjBoSEpNcEZ4YUI0SFVodkVWV2M4UFF6UG04ZGVaSlJXaWtZRjVCc2hFMkRPNXMzckkvbGdPckY2QjBDS0JyYk1wQS9sZnpjNjd0WUNTbVEyeDZyKzUzR0hGa3ZQQjNwMXlUSzBVeWFwNTlZS2srendzTVdpTkc2KzczVTBDRjVKM2NnUmRkYlAwL1VmREkrb2tTZ2FpNmxFOEYrTk92V0ZJVXMwQzRTeE9mazNvYU1qdnE3NU1MdlNlVHFwODFrbkxUbUNEcWpSZ1Y5UHNMcTYrVGhvMHY5cVJST2o5M0cyR2tWTkpJc256WFU0cUxZZFpQZGRneWgxQXJQNHptSVJRaVNxUDhIZmNwYktDRGMwSHJhdlpiaHY1UnpyL213aFdnL0YzSWFrRmVpTDc5WGp3QjBVYVdpL2VTZTBrNVB4TzJwaDRjZjI1dHJERC9GR3lhOEJQbGdKRm00ZUdCQzM1Si9WRnU2TWNWQkhnVXhPRklEU2Z4ZVZjMWo4M2crMC8ySWZ3QXFHTW9sajhzdThVakpYRTUiLCJtYWMiOiIzMmI5NDNmNDU5OTNjZWE4MjczZmEzMzEwZWIxZDEwYjhhODIwNjhiNTk1MjQyYjU3MzliMjI0ZTk4M2ZjNjYxIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlNiTHQvZWlITmpBeVRCRzRwQmZGMUE9PSIsInZhbHVlIjoiZEdqSWVDUnlmMkt2ZTNJRFpYQ2pTTjRBcjdQSTlBU0VnaEJncGhVQmF5TFRVQS9tWG9kQ0hJU3RJbFp1S2RSdXRqa01BcWVDRTVhcHNBbXo3K2tBbHRJUVNOZkxWRytRdG55TFJlLzgzOVkyMktKWlRHTk1LZk9hYWVzczIyNEtvcTB5NkZ6eWhlUjVHcHRmYWhzTDNSRjNVQjRNVVFOampsbHIvR1BySHRXM3lyV2ZNNG1yMVlraFNlY0VIS3VoSUNlU0U4ZkhROGZEZE1rSWtqOVVGa2lvTGh0ZVdaS29UR20zeVpueTBRUzZKR0RNck83TDM4dzhmUUxTQlBnVkhURllmUHRFM09QQmtnYllsVmhCZXhMbmdxcGxzd091RE5ZZTRvYTczVHdhSUNPQjZpYzNlMnJqaUMvbnNiL0grdTd5ci94VW14Nlo3VzhtTUx3ZVhVS2ZkalVGd2hKdFd5QTJUcTFxcjYxanB1Q0h3eXNlNnIrVXJlN2lsU0FWUHpxQkJZMXhBK2d2OUY5RXE5MW1ENkk5WlZsZWpRTGZramdvM25iQ2xBaTY3bTFtNUJGWVhyT202cVMrV01VMTVLUjBRQml0WFYvVGVHWmVMYlhNeWt0Q0hEMVVwQUQxaWtUdTNnNHBaeURtZ1JqQkllVE45a0RBRmhwVVdPMGYiLCJtYWMiOiI1OWU5NmY5ZDViM2Y3MGFhMDU2Nzg0YjI0NmMzMzg5ZDRjZDZhNGYyYzFiZjQyYzk4YWJjZWYxZjUxODg4YjI1IiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1976645934\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-885508258 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885508258\", {\"maxDepth\":0})</script>\n"}}