{"__meta": {"id": "X01398505543346e8a66c1104602182d9", "datetime": "2025-06-17 12:19:21", "utime": **********.902456, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.309931, "end": **********.90248, "duration": 0.5925488471984863, "duration_str": "593ms", "measures": [{"label": "Booting", "start": **********.309931, "relative_start": 0, "end": **********.817593, "relative_end": **********.817593, "duration": 0.5076620578765869, "duration_str": "508ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.817604, "relative_start": 0.5076730251312256, "end": **********.902482, "relative_end": 2.1457672119140625e-06, "duration": 0.08487796783447266, "duration_str": "84.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45162800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01615, "accumulated_duration_str": "16.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 16 limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8559701, "duration": 0.01314, "duration_str": "13.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "ty", "start_percent": 0, "width_percent": 81.362}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.880424, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "ty", "start_percent": 81.362, "width_percent": 6.44}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (16) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\to\\newo\\3\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\to\\newo\\3\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.889704, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "ty", "start_percent": 87.802, "width_percent": 12.198}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Fto%2Fnewo%2F3%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "16", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1148335079 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1148335079\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-706042563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-706042563\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-367185904 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367185904\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1962843041 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1937 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=1pubyvl%7C2%7Cfwu%7C0%7C1993; _clsk=25xk9k%7C1750162725167%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1VVDJrdGZqUWJFRkZyeVYrd242VHc9PSIsInZhbHVlIjoiaVorQVNnSDRDaFhGWUVPZkx4allZODJMRG5vOHA2Sk1vdHpHZTBMbnFyWnA0MVJBT0NmMDduSTRDQ081c0ZWd1hWZUc3aXRKV2I0aWN2MmtIRWZCN3piLzJWU1hlckxWQ0tzMmdCR1ZPMHRXL3JtMSsvSktoTWllSXhvc2ZEeFJMbHFZajB0Sk82SWpkZ1RSNHRYaGowYmdROWl6OGxFeSswZGFuM2RJSG15SXNSc3N1ZmxPQksrQjIzNmZ3UEtpbGNOeFhMVk81cWoya2hGNTJnOU0xTzNyUzV2ejJwRGxQb3NrVm5NdHRoWjV2REFxUTU5MHdZVjlYMkVvS25xSDFyM2J3QWhJTXpqNXdRL3ppazgxY3BKUzBlUFdZMlAzRStMbHROdHU2T1Fpd0JVRGJBQTJoeEUzOHBrS3E5MU5uR1duMytxd3UwalRieDRscUpaTGIyRlY2TlNUWWd1SU9DQUVFemE3ZFRSU3ljSlU2QWtoVjArcDJ4NEwxRVZHbFR4eWpiQ3czRzcwYW1lVFVwdWlCbWx2LytnZXRkVm9Xd1FFMFE1SS9HNC80ZU5mbE93YXRYajh4anpKRE9xaWxFZWw5TFQ1QzVkd3hRclByYmRCSUw5NmV6VDVnamo3T1VZbG0rVW5saGg0T1BrR25jYmwyeW5yM1BNMU15bGEiLCJtYWMiOiJjZDVkMjlkOTdjNzExYTA4MmQ1N2I5YzA4Yzc0OTgwNzM1ZjA4ZGQ0N2M4ZTE5NGY3YzJhM2YzYWVlNDkzNmU4IiwidGFnIjoiIn0%3D; test_session=eyJpdiI6IjZNRXQvTWRLY3g1UjdrazlybDJSSFE9PSIsInZhbHVlIjoiNjJQOUhFZEdTU2F4T1RwTExQRXFCelpDelJzTmZFaFRuU3NxcVRMbHA3RUpDMm1uUEpBMGpBQ3J1WGNTNzVGL1FUd0dPTlZPa3ltcytQZlFRd25ERVI4YVlPR0tOS0Y0WURVL0ZQbmMyR2JBNkg0OHZPcTZscEdtek1qdm5oRTUrakxzYnNtcEhEb2VVNEJsajh6NXJNTE92aDFLbTBpMDkwajV1bGxqUHl1WVZVeXZuTzdnb2xqN0dyNFVieHdDUzJ5YkFMa2N5VU5icGMybmkyaC9KdjdzbVZSR0wyNEMwVy91MU9hR0swVlEyVTdCTnlwbUMxU1M3V2pJaG5WWk9LVStzQndZOGZSczJVN2IyTUpad1lWR3lUZFhQUGZ3UDdrUzJoNk1yblFIWlUwWHVNa0srSjNzUWtNczRpaHFlNDZ3U1h0d3NhR1p0blBXQ1lYOUpKSkFYTDJPczFJMjVNR3N4ZWdjc2dZZjlvckd0WHpvcllkMFlDVHFQWXhrdGVWM2RHTmY0RXliblFXUXNjaGhIbzQvUVFBK1JoY0l3djNCclE3Wmo4MFhDZjJqV3dZaC9xSUtiQTVXbXp1Mmp3SWNJa2lPVmxReVhHWk03eFN5Ni9EbmVJQzFHb0RORGRmSk9yVDlsYldwMkhCVmcreWpxM0lyS2QzWERyVXciLCJtYWMiOiIzOTQ5NTY0YTkwOTZmZmU4OTAwYzVmOGY2ZDNlNTdiMmE2Y2ZmODU0MWEyNGM1NjRhMTZjMWM2ZDExMGI2YjgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1962843041\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-6519877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>test_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x6J25WmpCphH0nVhGWCYfOOOMFtTSb6zPGzaM2hl</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6519877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2114290617 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 17 Jun 2025 12:19:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhKK1M4L1hpOGtqbWxZclp5K0FYYXc9PSIsInZhbHVlIjoiZExjRDZLWlBFUEExVnlybUJpSUhHMC9lQTFoZlhoVlp4ei9RY29OVTZCZU5qYXNMdnFwRG85L0EyendwSmNqTkNIMGpKYnltT1RxaEZUQW1ROGFObkZOMk1JNnJobDdjdGp2ZHZJTVhNK2ZwaGQ0V2hqL2dCdWpMenlyR3FRaGRXaTlqcWJmVXV6TFJtMitXVnJOQUh0cmVxTmJLc21rSTBvb0VuYXljTEZXM2h4L085U011ZnpNcDdEV1hLZWtGU2FjbGtNZFFnS1ltV1Q1TmNxbzY2N3NQUTk1bU9lSWp5UUs2YmxoQXFGMjhKNnVQOGZHY1phQ0RxT2tWZlk5R0dZSTdpQkl3bHhzWnlzd0pwTkc5VEovdzM1MVpFblJqUVBFMXdKU2g2Mi9VaTZENzVQcFlvYWFJRTI1WUp6NVNVQ2R6amY2VlJia1hHRmY1czBveXVld054ak5HcHplKy9ZQnhUVFFJdGg4Y01iSWJCcXdpQWNNc3lSd3RpK3kyWGNkRkxjRFV1ZEtETCtKb01BY1NJTmZnbncvbkpEd1B4SngwRFhEVWMwOFNpYWIzM1AyK1p6QWY5Ulo0NGtqT2tpb0ZMb3M3eFNtOVc4WU02TUVaRVloV3JYUFd6Vk5oK3NZZDY0TlY5Y0tjWS9nMTVsT1c3NzBRQllmZ1FBVVgiLCJtYWMiOiIxZWFjMDdmYTJlN2JjOTc5NjZmMzM0NmIyMzc2ZDJiMGRhZjJmYmVmYjFkNmRlMTZiNzYxZjZkNTE5Nzc5ODMyIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"938 characters\">test_session=eyJpdiI6IlhJbldUbjVmMzBBWUFaVlltRi85VkE9PSIsInZhbHVlIjoiZkdEaXUwUjRJQ25VNy9Ga3p1eEpOQ2pNOVRsTWNicmNmNmsvazN6d1NoeTRjSTQ0Wi8zRDBJMHZEM2pQbXFvYVh6M1RRVGpaM3pwZHVOWHo1N0RkR01MeGJHMEpFeUJTY0w1R3Zlb0lCazV4U2ZkU2tJVXpDYjYyNUN1ZUR4ZERmS3ZFczhnd3NwVGNIRFcyOUhRTkVDM2s5UVUxbUJEYTFsVUdnRkxObWtmTmpTQzR6QW92YzZGR1VXb1hPWit3ZUZvTFk1aENqd0laazU2d1RwOVJwVGJkbGpQZjNCUlJ4Q3RCWUpkYW9jbzZISG52TkV2MDM1UVFrak1xdTc1UjBRbk1Nc3pUK1VpOXh1L2VJc2NFMXpQUEp3a29BWUg4bW9BaXB0eHdyeFB1WHNqbTdtV1ExOHArTjFrV255VGs1YXVTdjFZZUNNTVN6WkRNODkzYWt2aHRtRHRIVUZMR0pwNld3dmtaeENKQlhxLzhtQXU2NEFXNzRtK0RYMUZ4ZnNpbGlpcC8zeXJ4NUs2bnBWc3FQN1JObmQveDFoeUpzNnVhRW5sZlB6Q2d0VUtpemFSeUZPZ0ZFMTExcUs0SUdTWUVrVnVrLzhrZjQvdTA5OUc3YWEwYWdSeDFVaFBrUFVFby9kcWpmNEN0MlllODNCSnU3Nkh2VlpkUmJ5N1YiLCJtYWMiOiJhOTQzYTkyYzMxMzI2ZmI5NjgzY2FhZTM2NDU3YTkxMzI4ZWExNWE3OGYwYTUxMjc4YjY2ZTQ2Y2UzZWMwNTRjIiwidGFnIjoiIn0%3D; expires=Tue, 17 Jun 2025 14:19:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhKK1M4L1hpOGtqbWxZclp5K0FYYXc9PSIsInZhbHVlIjoiZExjRDZLWlBFUEExVnlybUJpSUhHMC9lQTFoZlhoVlp4ei9RY29OVTZCZU5qYXNMdnFwRG85L0EyendwSmNqTkNIMGpKYnltT1RxaEZUQW1ROGFObkZOMk1JNnJobDdjdGp2ZHZJTVhNK2ZwaGQ0V2hqL2dCdWpMenlyR3FRaGRXaTlqcWJmVXV6TFJtMitXVnJOQUh0cmVxTmJLc21rSTBvb0VuYXljTEZXM2h4L085U011ZnpNcDdEV1hLZWtGU2FjbGtNZFFnS1ltV1Q1TmNxbzY2N3NQUTk1bU9lSWp5UUs2YmxoQXFGMjhKNnVQOGZHY1phQ0RxT2tWZlk5R0dZSTdpQkl3bHhzWnlzd0pwTkc5VEovdzM1MVpFblJqUVBFMXdKU2g2Mi9VaTZENzVQcFlvYWFJRTI1WUp6NVNVQ2R6amY2VlJia1hHRmY1czBveXVld054ak5HcHplKy9ZQnhUVFFJdGg4Y01iSWJCcXdpQWNNc3lSd3RpK3kyWGNkRkxjRFV1ZEtETCtKb01BY1NJTmZnbncvbkpEd1B4SngwRFhEVWMwOFNpYWIzM1AyK1p6QWY5Ulo0NGtqT2tpb0ZMb3M3eFNtOVc4WU02TUVaRVloV3JYUFd6Vk5oK3NZZDY0TlY5Y0tjWS9nMTVsT1c3NzBRQllmZ1FBVVgiLCJtYWMiOiIxZWFjMDdmYTJlN2JjOTc5NjZmMzM0NmIyMzc2ZDJiMGRhZjJmYmVmYjFkNmRlMTZiNzYxZjZkNTE5Nzc5ODMyIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"924 characters\">test_session=eyJpdiI6IlhJbldUbjVmMzBBWUFaVlltRi85VkE9PSIsInZhbHVlIjoiZkdEaXUwUjRJQ25VNy9Ga3p1eEpOQ2pNOVRsTWNicmNmNmsvazN6d1NoeTRjSTQ0Wi8zRDBJMHZEM2pQbXFvYVh6M1RRVGpaM3pwZHVOWHo1N0RkR01MeGJHMEpFeUJTY0w1R3Zlb0lCazV4U2ZkU2tJVXpDYjYyNUN1ZUR4ZERmS3ZFczhnd3NwVGNIRFcyOUhRTkVDM2s5UVUxbUJEYTFsVUdnRkxObWtmTmpTQzR6QW92YzZGR1VXb1hPWit3ZUZvTFk1aENqd0laazU2d1RwOVJwVGJkbGpQZjNCUlJ4Q3RCWUpkYW9jbzZISG52TkV2MDM1UVFrak1xdTc1UjBRbk1Nc3pUK1VpOXh1L2VJc2NFMXpQUEp3a29BWUg4bW9BaXB0eHdyeFB1WHNqbTdtV1ExOHArTjFrV255VGs1YXVTdjFZZUNNTVN6WkRNODkzYWt2aHRtRHRIVUZMR0pwNld3dmtaeENKQlhxLzhtQXU2NEFXNzRtK0RYMUZ4ZnNpbGlpcC8zeXJ4NUs2bnBWc3FQN1JObmQveDFoeUpzNnVhRW5sZlB6Q2d0VUtpemFSeUZPZ0ZFMTExcUs0SUdTWUVrVnVrLzhrZjQvdTA5OUc3YWEwYWdSeDFVaFBrUFVFby9kcWpmNEN0MlllODNCSnU3Nkh2VlpkUmJ5N1YiLCJtYWMiOiJhOTQzYTkyYzMxMzI2ZmI5NjgzY2FhZTM2NDU3YTkxMzI4ZWExNWE3OGYwYTUxMjc4YjY2ZTQ2Y2UzZWMwNTRjIiwidGFnIjoiIn0%3D; expires=Tue, 17-Jun-2025 14:19:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114290617\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1258653040 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8xEDwWIeOR3chYNqeqkLuY1C4P2zktGsSCmubqIv</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>16</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1258653040\", {\"maxDepth\":0})</script>\n"}}