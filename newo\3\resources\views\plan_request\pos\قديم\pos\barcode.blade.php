@extends('layouts.admin')
@section('page-title')
    {{__('POS Product Barcode')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('POS Product Barcode')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
    <style>
        .product_barcode_hight_de {
            margin: 0 auto;
        }
        .barcode-box {
            border: 1px solid #000;
            padding: 10px;
            margin: 5px;
            text-align: center;
            display: inline-block;
            width: 300px;
            height: 120px;
            position: relative;
        }
        .product-name {
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
            text-align: center;
        }
        .product-price {
            font-size: 12px;
            margin-top: 3px;
            text-align: center;
        }
        .company-logo {
            position: absolute;
            top: 10px;
            right: 10px;
            max-width: 50px;
            max-height: 50px;
        }
    </style>
@endpush

@section('action-btn')
    <div class="float-end">
        @can('create barcode')
            <a href="{{ route('pos.barcode.html') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
               class="btn btn-sm btn-info me-1" data-bs-toggle="tooltip" title="معاينة HTML" target="_blank">
                <i class="ti ti-eye text-white"></i> معاينة
            </a>
            <a href="{{ route('pos.barcode.pdf') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
               class="btn btn-sm btn-success me-1" data-bs-toggle="tooltip" title="طباعة PDF مبسط">
                <i class="ti ti-file-type-pdf text-white"></i> PDF مبسط
            </a>
            <a href="{{ route('pos.print') }}" class="btn btn-sm btn-primary-subtle me-1" data-bs-toggle="tooltip" title="{{__('Print Barcode')}}">
                <i class="ti ti-scan text-white"></i>
            </a>
            <a data-url="{{ route('pos.setting') }}" data-ajax-popup="true" data-bs-toggle="tooltip" data-title="{{__('Barcode Setting')}}" title="{{__('Barcode Setting')}}" class="btn btn-sm btn-primary">
                <i class="ti ti-settings text-white"></i>
            </a>
        @endcan

    </div>
@endsection

@section('content')
    <!-- فلتر المستودعات -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('pos.barcode') }}" class="row align-items-end">
                        <div class="col-md-4">
                            <label for="warehouse_id" class="form-label">{{ __('اختر المستودع') }}</label>
                            <select name="warehouse_id" id="warehouse_id" class="form-control" onchange="this.form.submit()">
                                <option value="">{{ __('جميع المستودعات') }}</option>
                                @foreach($warehouses as $warehouse)
                                    <option value="{{ $warehouse->id }}"
                                        {{ request('warehouse_id') == $warehouse->id ? 'selected' : '' }}>
                                        {{ $warehouse->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">{{ __('عرض المنتجات') }}</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="only_available" id="only_available"
                                    value="1" {{ request('only_available') ? 'checked' : '' }} onchange="this.form.submit()">
                                <label class="form-check-label" for="only_available">
                                    {{ __('المنتجات المتوفرة فقط (كمية > 0)') }}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="ti ti-filter"></i> {{ __('تطبيق الفلتر') }}
                            </button>
                            <a href="{{ route('pos.barcode') }}" class="btn btn-secondary">
                                <i class="ti ti-refresh"></i> {{ __('إعادة تعيين') }}
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المنتجات -->
    <div class="row mt-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5>
                        @if(request('warehouse_id'))
                            {{ __('منتجات مستودع: ') }} {{ $warehouses->find(request('warehouse_id'))->name ?? '' }}
                        @else
                            {{ __('جميع المنتجات') }}
                        @endif
                        <span class="badge bg-primary ms-2">{{ $productServices->count() }} {{ __('منتج') }}</span>
                    </h5>
                </div>
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable-barcode" >
                            <thead>
                                <tr>
                                    <th>{{__('Product')}}</th>
                                    <th>{{ __('SKU') }}</th>
                                    <th>{{ __('Price') }}</th>
                                    @if(request('warehouse_id'))
                                        <th>{{__('كمية المستودع')}}</th>
                                    @endif
                                    <th>{{ __('Barcode') }}</th>
                                </tr>
                            </thead>

                            <tbody>
                                @forelse ($productServices as $productService)
                                    @php
                                        // Calculate price with tax
                                        $price = $productService->sale_price;
                                        $taxRate = !empty($productService->tax_id) ? $productService->taxRate($productService->tax_id) : 0;
                                        $taxAmount = ($taxRate / 100) * $price;
                                        $priceWithTax = $price + $taxAmount;
                                    @endphp
                                    <tr>
                                        <td>{{$productService->name}}</td>
                                        <td>{{$productService->sku}}</td>
                                        <td>{{\Auth::user()->priceFormat($priceWithTax)}}</td>
                                        @if(request('warehouse_id'))
                                            <td>
                                                <span class="badge {{ ($productService->warehouse_quantity ?? 0) > 0 ? 'bg-success' : 'bg-danger' }}">
                                                    {{ $productService->warehouse_quantity ?? 0 }}
                                                </span>
                                            </td>
                                        @endif
                                        <td>
                                            <div class="barcode-box">
                                                @php
                                                    $logo = asset(\Storage::url('uploads/logo/'));
                                                    $company_logo = \App\Models\Utility::GetLogo();
                                                @endphp
                                                <img src="{{ $logo . '/' . (isset($company_logo) && !empty($company_logo) ? $company_logo : 'logo-dark.png') }}" alt="Company Logo" class="company-logo">
                                                <div id="{{ $productService->id }}" class="product_barcode product_barcode_hight_de" data-skucode="{{ $productService->sku }}" data-name="{{ $productService->name }}" data-price="{{ \Auth::user()->priceFormat($priceWithTax) }}"></div>
                                                <div class="product-name">{{ $productService->name }}</div>
                                                <div class="product-price">{{ \Auth::user()->priceFormat($priceWithTax) }}</div>
                                            </div>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="5" class="text-center text-dark"><p>{{__('No Data Found')}}</p></td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
{{--    <script src="{{ asset('public/js/jquery-barcode.min.js') }}"></script>--}}
    <script src="{{ asset('public/js/jquery-barcode.js') }}"></script>
    <script>
        $(document).ready(function() {
            $(".product_barcode").each(function() {
                var id = $(this).attr("id");
                var sku = $(this).data('skucode');
                sku = encodeURIComponent(sku);
                generateBarcode(sku, id);
            });
        });
        function generateBarcode(val, id) {

            var value = val;
            var btype = '{{ $barcode['barcodeType'] }}';
            var renderer = '{{ $barcode['barcodeFormat'] }}';
            var settings = {
                output: renderer,
                bgColor: '#FFFFFF',
                color: '#000000',
                barWidth: '1',
                barHeight: '40',
                moduleSize: '5',
                posX: '10',
                posY: '20',
                addQuietZone: '1'
            };
            $('#' + id).html("").show().barcode(value, btype, settings);

        }

        setTimeout(myGreeting, 1000);
        function myGreeting() {
            if ($(".datatable-barcode").length > 0) {
                const dataTable =  new simpleDatatables.DataTable(".datatable-barcode");
            }
        }
        // });
    </script>

@endpush
